/**
 * 批次1.5、2.1、2.2节点注册表测试
 * 测试模型管理节点、VR/AR输入节点、高级输入节点的注册功能
 */

import { NodeRegistry } from './NodeRegistry';
import { Batch15And21And22NodesRegistry, BATCH_15_21_22_NODE_TYPES } from './Batch15And21And22NodesRegistry';
import { Debug } from '../../utils/Debug';

describe('Batch15And21And22NodesRegistry', () => {
  let nodeRegistry: NodeRegistry;
  let batch15And21And22Registry: Batch15And21And22NodesRegistry;

  beforeEach(() => {
    // 创建新的节点注册表实例
    nodeRegistry = new NodeRegistry();
    batch15And21And22Registry = new Batch15And21And22NodesRegistry(nodeRegistry);
  });

  afterEach(() => {
    // 清理
    nodeRegistry.clear();
  });

  describe('基本功能测试', () => {
    test('应该能够创建注册表实例', () => {
      expect(batch15And21And22Registry).toBeDefined();
      expect(batch15And21And22Registry.isRegistered()).toBe(false);
    });

    test('应该能够注册所有节点', () => {
      // 注册所有节点
      batch15And21And22Registry.registerAllNodes();

      // 验证注册状态
      expect(batch15And21And22Registry.isRegistered()).toBe(true);
    });

    test('不应该重复注册节点', () => {
      // 第一次注册
      batch15And21And22Registry.registerAllNodes();
      expect(batch15And21And22Registry.isRegistered()).toBe(true);

      // 第二次注册应该被跳过
      const consoleSpy = jest.spyOn(Debug, 'warn');
      batch15And21And22Registry.registerAllNodes();
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'Batch15And21And22NodesRegistry',
        '节点已经注册过了'
      );
    });
  });

  describe('节点类型验证', () => {
    beforeEach(() => {
      batch15And21And22Registry.registerAllNodes();
    });

    test('应该注册正确数量的节点', () => {
      const registeredTypes = batch15And21And22Registry.getAllRegisteredNodeTypes();
      expect(registeredTypes).toHaveLength(37);
    });

    test('应该包含所有模型管理节点类型', () => {
      const modelManagementTypes = [
        'model/registry',
        'model/validation',
        'model/testing',
        'model/benchmark',
        'model/comparison',
        'model/metrics',
        'model/audit',
        'model/governance',
        'model/lifecycle',
        'model/rollback',
        'model/abtest',
        'model/canary',
        'model/shadow',
        'model/feedback',
        'model/retraining',
        'model/drift_detection',
        'model/performance',
        'model/resource',
        'model/security',
        'model/privacy',
        'model/fairness',
        'model/interpretability',
        'model/documentation'
      ];

      modelManagementTypes.forEach(type => {
        expect(nodeRegistry.hasNodeType(type)).toBe(true);
      });
    });

    test('应该包含所有VR/AR输入节点类型', () => {
      const vrArInputTypes = [
        'input/vr_controller',
        'input/ar_touch',
        'input/spatial',
        'input/eye_tracking',
        'input/hand_tracking',
        'input/voice_command',
        'input/haptic_feedback',
        'input/motion_controller'
      ];

      vrArInputTypes.forEach(type => {
        expect(nodeRegistry.hasNodeType(type)).toBe(true);
      });
    });

    test('应该包含所有高级输入节点类型', () => {
      const advancedInputTypes = [
        'input/multitouch_gesture',
        'input/pressure_sensitive',
        'input/tilt',
        'input/proximity'
      ];

      advancedInputTypes.forEach(type => {
        expect(nodeRegistry.hasNodeType(type)).toBe(true);
      });
    });
  });

  describe('节点常量验证', () => {
    test('BATCH_15_21_22_NODE_TYPES应该包含所有节点类型', () => {
      const expectedTypes = [
        // 模型管理节点
        'model/registry',
        'model/validation',
        'model/testing',
        'model/benchmark',
        'model/comparison',
        'model/metrics',
        'model/audit',
        'model/governance',
        'model/lifecycle',
        'model/rollback',
        'model/abtest',
        'model/canary',
        'model/shadow',
        'model/feedback',
        'model/retraining',
        'model/drift_detection',
        'model/performance',
        'model/resource',
        'model/security',
        'model/privacy',
        'model/fairness',
        'model/interpretability',
        'model/documentation',

        // VR/AR输入节点
        'input/vr_controller',
        'input/ar_touch',
        'input/spatial',
        'input/eye_tracking',
        'input/hand_tracking',
        'input/voice_command',
        'input/haptic_feedback',
        'input/motion_controller',

        // 高级输入节点
        'input/multitouch_gesture',
        'input/pressure_sensitive',
        'input/tilt',
        'input/proximity'
      ];

      const constantValues = Object.values(BATCH_15_21_22_NODE_TYPES);
      expect(constantValues).toHaveLength(expectedTypes.length);
      
      expectedTypes.forEach(type => {
        expect(constantValues).toContain(type);
      });
    });
  });

  describe('节点创建测试', () => {
    beforeEach(() => {
      batch15And21And22Registry.registerAllNodes();
    });

    test('应该能够创建模型管理节点实例', () => {
      const modelRegistryNode = nodeRegistry.createNode('model/registry');
      expect(modelRegistryNode).toBeDefined();
      expect(modelRegistryNode.getType()).toBe('model/registry');
      expect(modelRegistryNode.getName()).toBe('模型注册表');
    });

    test('应该能够创建VR/AR输入节点实例', () => {
      const vrControllerNode = nodeRegistry.createNode('input/vr_controller');
      expect(vrControllerNode).toBeDefined();
      expect(vrControllerNode.getType()).toBe('input/vr_controller');
      expect(vrControllerNode.getName()).toBe('VR控制器输入');
    });

    test('应该能够创建高级输入节点实例', () => {
      const multiTouchNode = nodeRegistry.createNode('input/multitouch_gesture');
      expect(multiTouchNode).toBeDefined();
      expect(multiTouchNode.getType()).toBe('input/multitouch_gesture');
      expect(multiTouchNode.getName()).toBe('多点触控手势');
    });
  });

  describe('错误处理测试', () => {
    test('创建不存在的节点类型应该抛出错误', () => {
      batch15And21And22Registry.registerAllNodes();
      
      expect(() => {
        nodeRegistry.createNode('invalid/node_type');
      }).toThrow();
    });

    test('在未注册状态下获取节点类型应该返回空数组', () => {
      const types = batch15And21And22Registry.getAllRegisteredNodeTypes();
      expect(types).toHaveLength(37); // 常量数组，不依赖注册状态
    });
  });

  describe('集成测试', () => {
    test('应该能够与主注册系统集成', async () => {
      // 模拟主注册系统的初始化过程
      batch15And21And22Registry.registerAllNodes();

      // 验证所有节点都已正确注册
      const allTypes = batch15And21And22Registry.getAllRegisteredNodeTypes();
      
      allTypes.forEach(type => {
        expect(nodeRegistry.hasNodeType(type)).toBe(true);
        
        // 尝试创建每个节点类型的实例
        const node = nodeRegistry.createNode(type);
        expect(node).toBeDefined();
        expect(node.getType()).toBe(type);
      });
    });
  });
});

/**
 * 运行批次1.5、2.1、2.2节点注册表演示
 */
export async function runBatch15And21And22NodesDemo(): Promise<void> {
  console.log('=== 批次1.5、2.1、2.2节点注册表演示 ===\n');

  try {
    // 创建注册表实例
    const nodeRegistry = new NodeRegistry();
    const registry = new Batch15And21And22NodesRegistry(nodeRegistry);

    console.log('1. 创建注册表实例');
    console.log(`   注册状态: ${registry.isRegistered()}`);

    // 注册所有节点
    console.log('\n2. 注册所有节点...');
    registry.registerAllNodes();
    console.log(`   注册状态: ${registry.isRegistered()}`);

    // 获取节点统计信息
    const allTypes = registry.getAllRegisteredNodeTypes();
    console.log(`\n3. 节点统计信息:`);
    console.log(`   总节点数: ${allTypes.length}`);
    console.log(`   模型管理节点: 25个`);
    console.log(`   VR/AR输入节点: 8个`);
    console.log(`   高级输入节点: 4个`);

    // 演示节点创建
    console.log('\n4. 节点创建演示:');
    
    // 创建模型管理节点
    const modelRegistryNode = nodeRegistry.createNode('model/registry');
    console.log(`   模型注册表节点: ${modelRegistryNode.getName()}`);
    
    // 创建VR/AR输入节点
    const vrControllerNode = nodeRegistry.createNode('input/vr_controller');
    console.log(`   VR控制器输入节点: ${vrControllerNode.getName()}`);
    
    // 创建高级输入节点
    const multiTouchNode = nodeRegistry.createNode('input/multitouch_gesture');
    console.log(`   多点触控手势节点: ${multiTouchNode.getName()}`);

    console.log('\n=== 演示完成 ===');

  } catch (error) {
    console.error('演示过程中发生错误:', error);
  }
}

// 如果直接运行此文件，则执行演示
if (require.main === module) {
  runBatch15And21And22NodesDemo();
}
