/**
 * 数字人制作系统节点 - 第四部分
 * 包含行为响应、身体动画控制、手势动画控制等节点
 */

import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 行为响应节点
 * 根据用户行为做出响应
 */
export class BehaviorResponseNode extends VisualScriptNode {
  static readonly TYPE = 'BehaviorResponse';
  static readonly NAME = '行为响应';

  constructor() {
    super();
    this.name = BehaviorResponseNode.NAME;
    this.description = '根据用户行为做出响应';
    this.category = '数字人制作';
    
    // 输入
    this.addInput('userBehavior', 'string', '用户行为', '');
    this.addInput('behaviorContext', 'object', '行为上下文');
    this.addInput('responseMode', 'string', '响应模式', 'adaptive');
    this.addInput('personalityType', 'string', '性格类型', 'friendly');
    this.addInput('culturalContext', 'string', '文化背景', 'chinese');
    
    // 输出
    this.addOutput('responseAction', 'string', '响应动作');
    this.addOutput('responseExpression', 'string', '响应表情');
    this.addOutput('responseText', 'string', '响应文本');
    this.addOutput('responseGesture', 'string', '响应手势');
    this.addOutput('responseTriggered', 'boolean', '响应触发');
  }

  protected executeImpl(): any {
    const inputs = this.getInputValues();
    
    const behaviorAnalysis = this.analyzeBehavior(inputs.userBehavior, inputs.behaviorContext);
    const response = this.generateResponse(
      behaviorAnalysis,
      inputs.personalityType,
      inputs.culturalContext
    );

    Debug.log('BehaviorResponseNode', `行为响应: ${inputs.userBehavior} -> ${response.action}`);

    return {
      responseAction: response.action,
      responseExpression: response.expression,
      responseText: response.text,
      responseGesture: response.gesture,
      responseTriggered: true
    };
  }

  private analyzeBehavior(behavior: string, context: any): any {
    const behaviorPatterns = {
      'approach': { type: 'social', intensity: 0.7, intent: 'interaction' },
      'wave': { type: 'greeting', intensity: 0.8, intent: 'friendly' },
      'point': { type: 'directive', intensity: 0.6, intent: 'inquiry' },
      'nod': { type: 'agreement', intensity: 0.5, intent: 'confirmation' },
      'shake_head': { type: 'disagreement', intensity: 0.6, intent: 'rejection' },
      'smile': { type: 'positive', intensity: 0.9, intent: 'friendly' },
      'frown': { type: 'negative', intensity: 0.7, intent: 'concern' },
      'clap': { type: 'appreciation', intensity: 0.8, intent: 'approval' },
      'thumbs_up': { type: 'approval', intensity: 0.8, intent: 'positive' },
      'lean_forward': { type: 'interest', intensity: 0.6, intent: 'engagement' }
    };

    return behaviorPatterns[behavior] || { 
      type: 'unknown', 
      intensity: 0.3, 
      intent: 'neutral' 
    };
  }

  private generateResponse(analysis: any, personality: string, culture: string): any {
    const responses = {
      'social': {
        'friendly': {
          action: 'approach_user',
          expression: 'warm_smile',
          text: '您好！很高兴见到您！',
          gesture: 'wave'
        },
        'professional': {
          action: 'acknowledge_presence',
          expression: 'polite_smile',
          text: '您好，欢迎光临！',
          gesture: 'slight_bow'
        }
      },
      'greeting': {
        'friendly': {
          action: 'return_greeting',
          expression: 'happy',
          text: '您好！',
          gesture: 'wave_back'
        },
        'professional': {
          action: 'formal_greeting',
          expression: 'neutral_positive',
          text: '您好，很高兴为您服务！',
          gesture: 'nod'
        }
      },
      'positive': {
        'friendly': {
          action: 'mirror_emotion',
          expression: 'happy',
          text: '我也很开心！',
          gesture: 'smile_gesture'
        },
        'professional': {
          action: 'acknowledge_positive',
          expression: 'pleased',
          text: '很高兴您满意！',
          gesture: 'nod'
        }
      },
      'negative': {
        'friendly': {
          action: 'show_concern',
          expression: 'concerned',
          text: '有什么我可以帮助您的吗？',
          gesture: 'open_hands'
        },
        'professional': {
          action: 'offer_assistance',
          expression: 'attentive',
          text: '请告诉我如何为您提供帮助。',
          gesture: 'lean_forward'
        }
      }
    };

    const personalityResponses = responses[analysis.type]?.[personality] || 
                                responses['social']?.[personality] ||
                                responses['social']['friendly'];

    return personalityResponses;
  }
}

/**
 * 身体动画控制节点
 * 控制身体动作动画
 */
export class BodyAnimationControlNode extends VisualScriptNode {
  static readonly TYPE = 'BodyAnimationControl';
  static readonly NAME = '身体动画控制';

  constructor() {
    super();
    this.name = BodyAnimationControlNode.NAME;
    this.description = '控制身体动作动画';
    this.category = '数字人制作';
    
    // 输入
    this.addInput('animationType', 'string', '动画类型', 'idle');
    this.addInput('intensity', 'number', '动作强度', 1.0);
    this.addInput('speed', 'number', '动作速度', 1.0);
    this.addInput('blendMode', 'string', '混合模式', 'override');
    this.addInput('loopMode', 'string', '循环模式', 'loop');
    this.addInput('transitionTime', 'number', '过渡时间', 0.3);
    
    // 输出
    this.addOutput('animationData', 'object', '动画数据');
    this.addOutput('boneTransforms', 'array', '骨骼变换');
    this.addOutput('isPlaying', 'boolean', '正在播放');
    this.addOutput('progress', 'number', '播放进度');
  }

  protected executeImpl(): any {
    const inputs = this.getInputValues();
    
    const animationData = {
      type: inputs.animationType || 'idle',
      intensity: Math.max(0, Math.min(2, inputs.intensity || 1.0)),
      speed: Math.max(0.1, Math.min(3, inputs.speed || 1.0)),
      blendMode: inputs.blendMode || 'override',
      loopMode: inputs.loopMode || 'loop',
      transitionTime: inputs.transitionTime || 0.3,
      startTime: Date.now()
    };

    const boneTransforms = this.generateBoneTransforms(animationData);
    const progress = 0; // 刚开始播放

    Debug.log('BodyAnimationControlNode', `身体动画: ${animationData.type} (强度: ${animationData.intensity})`);

    return {
      animationData,
      boneTransforms,
      isPlaying: true,
      progress
    };
  }

  private generateBoneTransforms(animationData: any): any[] {
    const boneNames = [
      'Hips', 'Spine', 'Spine1', 'Spine2', 'Neck', 'Head',
      'LeftShoulder', 'LeftArm', 'LeftForeArm', 'LeftHand',
      'RightShoulder', 'RightArm', 'RightForeArm', 'RightHand',
      'LeftUpLeg', 'LeftLeg', 'LeftFoot',
      'RightUpLeg', 'RightLeg', 'RightFoot'
    ];

    const transforms = [];
    
    for (const boneName of boneNames) {
      const transform = this.getBoneTransform(boneName, animationData);
      transforms.push({
        boneName,
        position: transform.position,
        rotation: transform.rotation,
        scale: transform.scale
      });
    }

    return transforms;
  }

  private getBoneTransform(boneName: string, animationData: any): any {
    const baseTransform = {
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0, w: 1 },
      scale: { x: 1, y: 1, z: 1 }
    };

    // 根据动画类型和骨骼名称生成变换
    switch (animationData.type) {
      case 'wave':
        if (boneName === 'RightArm') {
          baseTransform.rotation.z = Math.sin(Date.now() * 0.01) * animationData.intensity * 0.5;
        }
        break;
      case 'nod':
        if (boneName === 'Head') {
          baseTransform.rotation.x = Math.sin(Date.now() * 0.008) * animationData.intensity * 0.3;
        }
        break;
      case 'walk':
        if (boneName === 'Hips') {
          baseTransform.position.y = Math.sin(Date.now() * 0.01 * animationData.speed) * 0.05 * animationData.intensity;
        }
        break;
      case 'idle':
        // 轻微的呼吸动作
        if (boneName === 'Spine1') {
          baseTransform.scale.y = 1 + Math.sin(Date.now() * 0.003) * 0.02 * animationData.intensity;
        }
        break;
    }

    return baseTransform;
  }
}

/**
 * 手势动画控制节点
 * 控制手势动作
 */
export class GestureAnimationControlNode extends VisualScriptNode {
  static readonly TYPE = 'GestureAnimationControl';
  static readonly NAME = '手势动画控制';

  constructor() {
    super();
    this.name = GestureAnimationControlNode.NAME;
    this.description = '控制手势动作';
    this.category = '数字人制作';
    
    // 输入
    this.addInput('gestureType', 'string', '手势类型', 'point');
    this.addInput('handSide', 'string', '手部选择', 'right');
    this.addInput('intensity', 'number', '手势强度', 1.0);
    this.addInput('duration', 'number', '持续时间', 2.0);
    this.addInput('targetPosition', 'Vector3', '目标位置');
    this.addInput('expressiveness', 'number', '表现力', 0.8);
    
    // 输出
    this.addOutput('gestureData', 'object', '手势数据');
    this.addOutput('handTransforms', 'array', '手部变换');
    this.addOutput('fingerPositions', 'array', '手指位置');
    this.addOutput('isExecuting', 'boolean', '正在执行');
  }

  protected executeImpl(): any {
    const inputs = this.getInputValues();
    
    const gestureData = {
      type: inputs.gestureType || 'point',
      handSide: inputs.handSide || 'right',
      intensity: Math.max(0, Math.min(2, inputs.intensity || 1.0)),
      duration: inputs.duration || 2.0,
      targetPosition: inputs.targetPosition,
      expressiveness: inputs.expressiveness || 0.8,
      startTime: Date.now()
    };

    const handTransforms = this.generateHandTransforms(gestureData);
    const fingerPositions = this.generateFingerPositions(gestureData);

    Debug.log('GestureAnimationControlNode', `手势动画: ${gestureData.type} (${gestureData.handSide}手)`);

    return {
      gestureData,
      handTransforms,
      fingerPositions,
      isExecuting: true
    };
  }

  private generateHandTransforms(gestureData: any): any[] {
    const handBones = gestureData.handSide === 'right' ? 
      ['RightShoulder', 'RightArm', 'RightForeArm', 'RightHand'] :
      ['LeftShoulder', 'LeftArm', 'LeftForeArm', 'LeftHand'];

    const transforms = [];

    for (const boneName of handBones) {
      const transform = this.getGestureTransform(boneName, gestureData);
      transforms.push({
        boneName,
        position: transform.position,
        rotation: transform.rotation,
        scale: { x: 1, y: 1, z: 1 }
      });
    }

    return transforms;
  }

  private getGestureTransform(boneName: string, gestureData: any): any {
    const baseTransform = {
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0, w: 1 }
    };

    const intensity = gestureData.intensity;
    const isRight = gestureData.handSide === 'right';
    const multiplier = isRight ? 1 : -1;

    switch (gestureData.type) {
      case 'point':
        if (boneName.includes('Arm')) {
          baseTransform.rotation.y = multiplier * 0.3 * intensity;
          baseTransform.rotation.z = 0.2 * intensity;
        }
        if (boneName.includes('ForeArm')) {
          baseTransform.rotation.x = -0.1 * intensity;
        }
        break;

      case 'wave':
        if (boneName.includes('Arm')) {
          baseTransform.rotation.z = multiplier * Math.sin(Date.now() * 0.01) * 0.5 * intensity;
        }
        if (boneName.includes('ForeArm')) {
          baseTransform.rotation.y = Math.sin(Date.now() * 0.01 + Math.PI/4) * 0.3 * intensity;
        }
        break;

      case 'thumbs_up':
        if (boneName.includes('Arm')) {
          baseTransform.rotation.z = multiplier * 0.4 * intensity;
        }
        if (boneName.includes('ForeArm')) {
          baseTransform.rotation.x = -0.2 * intensity;
        }
        break;

      case 'open_palm':
        if (boneName.includes('Arm')) {
          baseTransform.rotation.x = 0.1 * intensity;
          baseTransform.rotation.z = multiplier * 0.2 * intensity;
        }
        break;

      case 'peace_sign':
        if (boneName.includes('Arm')) {
          baseTransform.rotation.z = multiplier * 0.3 * intensity;
        }
        if (boneName.includes('ForeArm')) {
          baseTransform.rotation.x = -0.3 * intensity;
        }
        break;
    }

    return baseTransform;
  }

  private generateFingerPositions(gestureData: any): any[] {
    const fingerNames = [
      'Thumb', 'Index', 'Middle', 'Ring', 'Pinky'
    ];

    const fingerPositions = [];

    for (const fingerName of fingerNames) {
      const position = this.getFingerPosition(fingerName, gestureData);
      fingerPositions.push({
        fingerName,
        joints: position.joints,
        curl: position.curl,
        spread: position.spread
      });
    }

    return fingerPositions;
  }

  private getFingerPosition(fingerName: string, gestureData: any): any {
    const basePosition = {
      joints: [0, 0, 0], // 三个关节的弯曲度
      curl: 0,
      spread: 0
    };

    switch (gestureData.type) {
      case 'point':
        if (fingerName === 'Index') {
          basePosition.joints = [0, 0, 0]; // 伸直
        } else {
          basePosition.joints = [0.8, 0.9, 0.7]; // 弯曲
          basePosition.curl = 0.8;
        }
        break;

      case 'thumbs_up':
        if (fingerName === 'Thumb') {
          basePosition.joints = [0, 0, 0]; // 伸直
        } else {
          basePosition.joints = [0.9, 1.0, 0.8]; // 弯曲
          basePosition.curl = 0.9;
        }
        break;

      case 'peace_sign':
        if (fingerName === 'Index' || fingerName === 'Middle') {
          basePosition.joints = [0, 0, 0]; // 伸直
          basePosition.spread = fingerName === 'Middle' ? 0.3 : -0.3;
        } else {
          basePosition.joints = [0.8, 0.9, 0.7]; // 弯曲
          basePosition.curl = 0.8;
        }
        break;

      case 'open_palm':
        basePosition.joints = [0, 0, 0]; // 所有手指伸直
        basePosition.spread = (fingerNames.indexOf(fingerName) - 2) * 0.2; // 展开
        break;

      default:
        // 默认放松状态
        basePosition.joints = [0.2, 0.3, 0.2];
        basePosition.curl = 0.2;
        break;
    }

    return basePosition;
  }
}

// 导出所有数字人节点类型
export const DIGITAL_HUMAN_NODE_TYPES = [
  'DigitalHumanEntity',
  'DigitalHumanModelLoader', 
  'DigitalHumanMaterial',
  'DigitalHumanAnimationBinding',
  'DigitalHumanPhysics',
  'DigitalHumanScenePlacement',
  'FacialExpressionControl',
  'EmotionStateManager',
  'ExpressionAnimation',
  'ExpressionSync',
  'MicroExpression',
  'SpeechRecognition',
  'SpeechSynthesis',
  'LipSync',
  'VoiceEmotion',
  'MultiLanguageSupport',
  'UserDetection',
  'GreetingBehavior',
  'DialogueManager',
  'BehaviorResponse',
  'BodyAnimationControl',
  'GestureAnimationControl'
] as const;

// 节点分类
export const DIGITAL_HUMAN_CATEGORIES = {
  CREATION: '数字人创建',
  EXPRESSION: '表情控制', 
  SPEECH: '语音系统',
  INTERACTION: '交互行为',
  ANIMATION: '动画控制'
} as const;
