/**
 * 批次3.2-3.7节点注册测试
 * 测试节点注册表功能
 */

import { describe, it, expect, beforeAll } from 'vitest';
import { batch32To37NodesRegistry } from '../../registry/Batch32To37NodesRegistry';

describe('批次3.2-3.7节点注册测试', () => {
  beforeAll(async () => {
    // 注册所有批次3.2-3.7节点
    await batch32To37NodesRegistry.registerAllNodes();
  });

  it('应该成功注册所有43个节点', () => {
    const registeredCount = batch32To37NodesRegistry.getRegisteredNodeCount();
    expect(registeredCount).toBe(43);
  });

  it('应该包含所有数字人制作系统节点类型', () => {
    const digitalHumanNodeTypes = [
      'DigitalHumanEntity',
      'DigitalHumanModelLoader',
      'DigitalHumanMaterial',
      'DigitalHumanAnimationBinding',
      'DigitalHumanPhysics',
      'DigitalHumanScenePlacement',
      'FacialExpressionControl',
      'EmotionStateManager',
      'ExpressionAnimation',
      'ExpressionSync',
      'MicroExpression',
      'SpeechRecognition',
      'SpeechSynthesis',
      'LipSync',
      'VoiceEmotion',
      'MultiLanguageSupport',
      'UserDetection',
      'GreetingBehavior',
      'DialogueManager',
      'BehaviorResponse',
      'BodyAnimationControl',
      'GestureAnimationControl'
    ];

    digitalHumanNodeTypes.forEach(nodeType => {
      expect(batch32To37NodesRegistry.isNodeRegistered(nodeType)).toBe(true);
    });
  });

  it('应该包含所有区块链节点类型', () => {
    const blockchainNodeTypes = [
      'SmartContract',
      'BlockchainTransaction',
      'Cryptocurrency'
    ];

    blockchainNodeTypes.forEach(nodeType => {
      expect(batch32To37NodesRegistry.isNodeRegistered(nodeType)).toBe(true);
    });
  });

  it('应该包含所有学习记录系统节点类型', () => {
    const learningNodeTypes = [
      'LearningRecord',
      'LearningStatistics',
      'AchievementSystem',
      'LearningPath',
      'KnowledgeGraph'
    ];

    learningNodeTypes.forEach(nodeType => {
      expect(batch32To37NodesRegistry.isNodeRegistered(nodeType)).toBe(true);
    });
  });

  it('应该包含所有RAG应用系统节点类型', () => {
    const ragNodeTypes = [
      'KnowledgeBase',
      'RAGQuery',
      'DocumentProcessing',
      'SemanticSearch',
      'DocumentIndex'
    ];

    ragNodeTypes.forEach(nodeType => {
      expect(batch32To37NodesRegistry.isNodeRegistered(nodeType)).toBe(true);
    });
  });

  it('应该包含所有协作功能节点类型', () => {
    const collaborationNodeTypes = [
      'RealTimeCollaboration',
      'VersionControl',
      'ConflictResolution',
      'PermissionManagement',
      'ActivityTracking',
      'NotificationSystem'
    ];

    collaborationNodeTypes.forEach(nodeType => {
      expect(batch32To37NodesRegistry.isNodeRegistered(nodeType)).toBe(true);
    });
  });

  it('应该包含所有第三方集成节点类型', () => {
    const integrationNodeTypes = [
      'WebhookIntegration',
      'APIGateway'
    ];

    integrationNodeTypes.forEach(nodeType => {
      expect(batch32To37NodesRegistry.isNodeRegistered(nodeType)).toBe(true);
    });
  });

  it('应该返回正确的注册统计信息', () => {
    const stats = batch32To37NodesRegistry.getRegistrationStats();

    expect(stats.digitalHumanNodes).toBe(22);
    expect(stats.blockchainNodes).toBe(3);
    expect(stats.learningRecordNodes).toBe(5);
    expect(stats.ragNodes).toBe(5);
    expect(stats.collaborationNodes).toBe(6);
    expect(stats.thirdPartyNodes).toBe(2);
    expect(stats.totalNodes).toBe(43);
  });

  it('应该返回所有已注册的节点类型', () => {
    const allNodeTypes = batch32To37NodesRegistry.getAllRegisteredNodeTypes();
    expect(allNodeTypes).toHaveLength(43);
    expect(allNodeTypes).toContain('DigitalHumanEntity');
    expect(allNodeTypes).toContain('SmartContract');
    expect(allNodeTypes).toContain('LearningRecord');
    expect(allNodeTypes).toContain('RAGQuery');
    expect(allNodeTypes).toContain('RealTimeCollaboration');
    expect(allNodeTypes).toContain('WebhookIntegration');
  });
});
