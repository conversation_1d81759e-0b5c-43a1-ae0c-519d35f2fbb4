/**
 * 批次2.3-3.1节点演示示例
 * 展示如何使用新注册的41个节点
 */

import { Debug } from '../engine/src/utils/Debug';

// 导入节点类
import { AccelerometerNode, GyroscopeNode } from '../engine/src/visual-script/nodes/input/SensorInputNodes';
import { SpeechRecognitionNode, VoiceActivityDetectionNode } from '../engine/src/visual-script/nodes/input/VoiceInputNodes';
import { HandGestureRecognitionNode } from '../engine/src/visual-script/nodes/input/GestureRecognitionNodes';
import { PaymentGatewayNode } from '../engine/src/visual-script/nodes/payment/PaymentSystemNodes';
import { SocialMediaIntegrationNode } from '../engine/src/visual-script/nodes/integration/ThirdPartyIntegrationNodes';
import { GISDataLoaderNode, NavigationNode } from '../engine/src/visual-script/nodes/spatial/SpatialInformationNodes';

/**
 * 传感器输入演示
 */
export function demonstrateSensorInput(): void {
  Debug.log('Demo', '=== 传感器输入节点演示 ===');

  // 创建加速度计节点
  const accelerometer = new AccelerometerNode();
  const accelResult = accelerometer.execute({
    enable: true,
    sensitivity: 1.0,
    frequency: 60
  });
  
  Debug.log('Demo', '加速度计数据:', {
    x: accelResult.accelerationX,
    y: accelResult.accelerationY,
    z: accelResult.accelerationZ,
    magnitude: accelResult.magnitude
  });

  // 创建陀螺仪节点
  const gyroscope = new GyroscopeNode();
  const gyroResult = gyroscope.execute({
    enable: true,
    sensitivity: 1.0
  });
  
  Debug.log('Demo', '陀螺仪数据:', {
    rotationX: gyroResult.rotationX,
    rotationY: gyroResult.rotationY,
    rotationZ: gyroResult.rotationZ,
    angularVelocity: gyroResult.angularVelocity
  });
}

/**
 * 语音输入演示
 */
export function demonstrateVoiceInput(): void {
  Debug.log('Demo', '=== 语音输入节点演示 ===');

  // 创建语音识别节点
  const speechRecognition = new SpeechRecognitionNode();
  const speechResult = speechRecognition.execute({
    enable: true,
    language: 'zh-CN',
    continuous: true,
    confidenceThreshold: 0.7
  });
  
  Debug.log('Demo', '语音识别结果:', {
    transcript: speechResult.transcript,
    confidence: speechResult.confidence,
    language: speechResult.language
  });

  // 创建语音活动检测节点
  const voiceActivity = new VoiceActivityDetectionNode();
  const vadResult = voiceActivity.execute({
    enable: true,
    threshold: 0.01,
    minDuration: 100
  });
  
  Debug.log('Demo', '语音活动检测:', {
    isActive: vadResult.isActive,
    audioLevel: vadResult.audioLevel,
    duration: vadResult.duration
  });
}

/**
 * 手势识别演示
 */
export function demonstrateGestureRecognition(): void {
  Debug.log('Demo', '=== 手势识别节点演示 ===');

  // 创建手势识别节点
  const gestureRecognition = new HandGestureRecognitionNode();
  const gestureResult = gestureRecognition.execute({
    enable: true,
    hand: 'both',
    confidenceThreshold: 0.8
  });
  
  Debug.log('Demo', '手势识别结果:', {
    gestureType: gestureResult.gestureType,
    confidence: gestureResult.confidence,
    hand: gestureResult.hand,
    position: gestureResult.position
  });
}

/**
 * 支付系统演示
 */
export function demonstratePaymentSystem(): void {
  Debug.log('Demo', '=== 支付系统节点演示 ===');

  // 创建支付网关节点
  const paymentGateway = new PaymentGatewayNode();
  const paymentResult = paymentGateway.execute({
    enable: true,
    gateway: 'stripe',
    amount: 99.99,
    currency: 'USD',
    paymentMethod: 'credit_card'
  });
  
  Debug.log('Demo', '支付处理结果:', {
    transactionId: paymentResult.transactionId,
    status: paymentResult.status,
    fees: paymentResult.fees,
    paymentUrl: paymentResult.paymentUrl
  });
}

/**
 * 第三方集成演示
 */
export function demonstrateThirdPartyIntegration(): void {
  Debug.log('Demo', '=== 第三方集成节点演示 ===');

  // 创建社交媒体集成节点
  const socialMedia = new SocialMediaIntegrationNode();
  const socialResult = socialMedia.execute({
    enable: true,
    platform: 'facebook',
    action: 'post',
    content: '这是一条测试消息',
    accessToken: 'mock_token_123'
  });
  
  Debug.log('Demo', '社交媒体发布结果:', {
    status: socialResult.status,
    postId: socialResult.postId,
    engagement: socialResult.engagement
  });
}

/**
 * 空间信息演示
 */
export function demonstrateSpatialInformation(): void {
  Debug.log('Demo', '=== 空间信息节点演示 ===');

  // 创建GIS数据加载节点
  const gisLoader = new GISDataLoaderNode();
  const gisResult = gisLoader.execute({
    enable: true,
    dataSource: 'file',
    format: 'geojson',
    coordinateSystem: 'WGS84'
  });
  
  Debug.log('Demo', 'GIS数据加载结果:', {
    featureCount: gisResult.featureCount,
    bounds: gisResult.bounds,
    coordinateSystem: gisResult.coordinateSystem
  });

  // 创建导航节点
  const navigation = new NavigationNode();
  const navResult = navigation.execute({
    enable: true,
    currentLocation: { lat: 39.9093, lng: 116.3974 },
    destination: { lat: 39.9193, lng: 116.4074 },
    navigationMode: 'driving'
  });
  
  Debug.log('Demo', '导航结果:', {
    remainingDistance: navResult.remainingDistance,
    estimatedTime: navResult.estimatedTime,
    nextTurn: navResult.nextTurn
  });
}

/**
 * 综合演示场景：智能交互应用
 */
export function demonstrateSmartInteractionApp(): void {
  Debug.log('Demo', '=== 智能交互应用综合演示 ===');

  // 1. 传感器检测用户动作
  const accelerometer = new AccelerometerNode();
  const motionData = accelerometer.execute({ enable: true });
  
  if (motionData.magnitude > 10) {
    Debug.log('Demo', '检测到用户运动，启动语音识别...');
    
    // 2. 语音识别用户指令
    const speechRecognition = new SpeechRecognitionNode();
    const speechData = speechRecognition.execute({
      enable: true,
      language: 'zh-CN'
    });
    
    Debug.log('Demo', `识别到语音指令: ${speechData.transcript}`);
    
    // 3. 手势识别确认操作
    const gestureRecognition = new HandGestureRecognitionNode();
    const gestureData = gestureRecognition.execute({ enable: true });
    
    if (gestureData.gestureType === 'thumbs_up') {
      Debug.log('Demo', '手势确认，执行支付操作...');
      
      // 4. 执行支付
      const paymentGateway = new PaymentGatewayNode();
      const paymentData = paymentGateway.execute({
        enable: true,
        amount: 29.99,
        currency: 'USD'
      });
      
      if (paymentData.status === 'completed') {
        Debug.log('Demo', '支付成功，分享到社交媒体...');
        
        // 5. 分享到社交媒体
        const socialMedia = new SocialMediaIntegrationNode();
        socialMedia.execute({
          enable: true,
          platform: 'twitter',
          action: 'post',
          content: '刚刚完成了一次智能支付体验！'
        });
      }
    }
  }
}

/**
 * 运行所有演示
 */
export function runAllDemos(): void {
  try {
    Debug.log('Demo', '🚀 开始批次2.3-3.1节点演示...');
    
    demonstrateSensorInput();
    demonstrateVoiceInput();
    demonstrateGestureRecognition();
    demonstratePaymentSystem();
    demonstrateThirdPartyIntegration();
    demonstrateSpatialInformation();
    demonstrateSmartInteractionApp();
    
    Debug.log('Demo', '✅ 所有演示完成！');
    
  } catch (error) {
    Debug.error('Demo', '演示过程中发生错误:', error);
  }
}

// 如果直接运行此文件，则执行演示
if (typeof window !== 'undefined' && (window as any).runBatch23To31Demo) {
  runAllDemos();
}

// 导出演示函数
export {
  runAllDemos as default,
  demonstrateSensorInput,
  demonstrateVoiceInput,
  demonstrateGestureRecognition,
  demonstratePaymentSystem,
  demonstrateThirdPartyIntegration,
  demonstrateSpatialInformation,
  demonstrateSmartInteractionApp
};
