/**
 * 批次3.2-3.7节点注册表
 * 注册数字人制作系统节点（22个）、区块链节点（3个）、学习记录系统节点（5个）、
 * RAG应用系统节点（5个）、协作功能节点（6个）、第三方集成节点（2个）
 * 总计43个节点
 */

import { NodeRegistry } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

// 导入数字人制作系统节点（22个）
import {
  DigitalHumanEntityNode,
  DigitalHumanModelLoaderNode,
  DigitalHumanMaterialNode,
  DigitalHumanAnimationBindingNode,
  DigitalHumanPhysicsNode,
  DigitalHumanScenePlacementNode,
  FacialExpressionControlNode,
  EmotionStateManagerNode,
  ExpressionAnimationNode
} from '../nodes/digital-human/DigitalHumanNodes';

import {
  ExpressionSyncNode,
  MicroExpressionNode,
  SpeechRecognitionNode,
  SpeechSynthesisNode,
  LipSyncNode
} from '../nodes/digital-human/DigitalHumanNodes2';

import {
  VoiceEmotionNode,
  MultiLanguageSupportNode,
  UserDetectionNode,
  GreetingBehaviorNode,
  DialogueManagerNode
} from '../nodes/digital-human/DigitalHumanNodes3';

import {
  BehaviorResponseNode,
  BodyAnimationControlNode,
  GestureAnimationControlNode
} from '../nodes/digital-human/DigitalHumanNodes4';

// 导入区块链节点（3个）
import {
  SmartContractNode,
  BlockchainTransactionNode,
  CryptocurrencyNode
} from '../nodes/blockchain/BlockchainNodes';

// 导入学习记录系统节点（5个）
import {
  LearningRecordNode,
  LearningStatisticsNode,
  AchievementSystemNode,
  LearningPathNode,
  KnowledgeGraphNode
} from '../nodes/learning/LearningRecordNodes';

// 导入RAG应用系统节点（5个）
import {
  KnowledgeBaseNode,
  RAGQueryNode,
  DocumentProcessingNode,
  SemanticSearchNode,
  DocumentIndexNode
} from '../nodes/rag/RAGApplicationNodes';

// 导入协作功能节点（6个）
import {
  RealTimeCollaborationNode,
  VersionControlNode,
  ConflictResolutionNode,
  PermissionManagementNode,
  ActivityTrackingNode,
  NotificationSystemNode
} from '../nodes/collaboration/CollaborationNodes';

// 导入第三方集成节点（2个）
import {
  WebhookIntegrationNode,
  APIGatewayNode
} from '../nodes/integration/ThirdPartyIntegrationNodes';

/**
 * 批次3.2-3.7节点注册表类
 */
export class Batch32To37NodesRegistry {
  private nodeRegistry: NodeRegistry;
  private registered: boolean = false;
  private registeredNodes: Map<string, any> = new Map();
  private registrationStats = {
    digitalHumanNodes: 0,
    blockchainNodes: 0,
    learningRecordNodes: 0,
    ragNodes: 0,
    collaborationNodes: 0,
    thirdPartyNodes: 0,
    totalNodes: 0
  };

  constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  /**
   * 注册所有批次3.2-3.7节点
   */
  public async registerAllNodes(): Promise<void> {
    if (this.registered) {
      console.log('批次3.2-3.7节点已注册，跳过重复注册');
      return;
    }

    console.log('开始注册批次3.2-3.7节点...');

    try {
      // 注册数字人制作系统节点（22个）
      await this.registerDigitalHumanNodes();

      // 注册区块链节点（3个）
      await this.registerBlockchainNodes();

      // 注册学习记录系统节点（5个）
      await this.registerLearningRecordNodes();

      // 注册RAG应用系统节点（5个）
      await this.registerRAGNodes();

      // 注册协作功能节点（6个）
      await this.registerCollaborationNodes();

      // 注册第三方集成节点（2个）
      await this.registerThirdPartyNodes();

      this.registered = true;
      this.calculateTotalStats();
      
      console.log(`批次3.2-3.7节点注册完成，共注册 ${this.registrationStats.totalNodes} 个节点`);
      console.log('详细统计:', this.registrationStats);

    } catch (error) {
      console.error('批次3.2-3.7节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册数字人制作系统节点（22个）
   */
  private async registerDigitalHumanNodes(): Promise<void> {
    console.log('注册数字人制作系统节点...');

    // 数字人创建节点（6个）
    const creationNodes = [
      { nodeClass: DigitalHumanEntityNode, type: 'DigitalHumanEntity', name: '数字人实体创建', desc: '创建数字人基础实体' },
      { nodeClass: DigitalHumanModelLoaderNode, type: 'DigitalHumanModelLoader', name: '数字人模型加载', desc: '加载3D人物模型' },
      { nodeClass: DigitalHumanMaterialNode, type: 'DigitalHumanMaterial', name: '数字人材质配置', desc: '配置皮肤、服装材质' },
      { nodeClass: DigitalHumanAnimationBindingNode, type: 'DigitalHumanAnimationBinding', name: '数字人动画绑定', desc: '绑定骨骼动画' },
      { nodeClass: DigitalHumanPhysicsNode, type: 'DigitalHumanPhysics', name: '数字人物理设置', desc: '设置物理属性' },
      { nodeClass: DigitalHumanScenePlacementNode, type: 'DigitalHumanScenePlacement', name: '数字人场景放置', desc: '在场景中放置数字人' }
    ];

    // 表情控制节点（5个）
    const expressionNodes = [
      { nodeClass: FacialExpressionControlNode, type: 'FacialExpressionControl', name: '面部表情控制', desc: '控制面部表情变化' },
      { nodeClass: EmotionStateManagerNode, type: 'EmotionStateManager', name: '情感状态管理', desc: '管理数字人情感状态' },
      { nodeClass: ExpressionAnimationNode, type: 'ExpressionAnimation', name: '表情动画播放', desc: '播放表情动画序列' },
      { nodeClass: ExpressionSyncNode, type: 'ExpressionSync', name: '表情同步', desc: '同步表情与语音' },
      { nodeClass: MicroExpressionNode, type: 'MicroExpression', name: '微表情控制', desc: '控制细微表情变化' }
    ];

    // 语音系统节点（5个）
    const speechNodes = [
      { nodeClass: SpeechRecognitionNode, type: 'SpeechRecognition', name: '语音识别', desc: '识别用户语音输入' },
      { nodeClass: SpeechSynthesisNode, type: 'SpeechSynthesis', name: '语音合成', desc: '合成数字人语音' },
      { nodeClass: LipSyncNode, type: 'LipSync', name: '口型同步', desc: '同步口型与语音' },
      { nodeClass: VoiceEmotionNode, type: 'VoiceEmotion', name: '语音情感', desc: '为语音添加情感色彩' },
      { nodeClass: MultiLanguageSupportNode, type: 'MultiLanguageSupport', name: '多语言支持', desc: '支持多种语言交互' }
    ];

    // 交互行为节点（4个）
    const interactionNodes = [
      { nodeClass: UserDetectionNode, type: 'UserDetection', name: '用户检测', desc: '检测用户接近和离开' },
      { nodeClass: GreetingBehaviorNode, type: 'GreetingBehavior', name: '主动问候', desc: '主动向用户问候' },
      { nodeClass: DialogueManagerNode, type: 'DialogueManager', name: '对话管理', desc: '管理对话流程和上下文' },
      { nodeClass: BehaviorResponseNode, type: 'BehaviorResponse', name: '行为响应', desc: '根据用户行为做出响应' }
    ];

    // 动画控制节点（2个）
    const animationNodes = [
      { nodeClass: BodyAnimationControlNode, type: 'BodyAnimationControl', name: '身体动画控制', desc: '控制身体动作动画' },
      { nodeClass: GestureAnimationControlNode, type: 'GestureAnimationControl', name: '手势动画控制', desc: '控制手势动作' }
    ];

    // 注册所有数字人节点
    const allDigitalHumanNodes = [
      ...creationNodes,
      ...expressionNodes,
      ...speechNodes,
      ...interactionNodes,
      ...animationNodes
    ];

    for (const { nodeClass, type, name, desc } of allDigitalHumanNodes) {
      this.nodeRegistry.registerNode(
        type,
        nodeClass,
        '数字人制作',
        desc,
        'person',
        '#E91E63'
      );
      this.registeredNodes.set(type, nodeClass);
    }

    this.registrationStats.digitalHumanNodes = allDigitalHumanNodes.length;
    console.log(`数字人制作系统节点注册完成 - ${allDigitalHumanNodes.length}个节点`);
  }

  /**
   * 注册区块链节点（3个）
   */
  private async registerBlockchainNodes(): Promise<void> {
    console.log('注册区块链节点...');

    const blockchainNodes = [
      { nodeClass: SmartContractNode, type: 'SmartContract', name: '智能合约', desc: '创建和执行智能合约' },
      { nodeClass: BlockchainTransactionNode, type: 'BlockchainTransaction', name: '区块链交易', desc: '处理区块链交易' },
      { nodeClass: CryptocurrencyNode, type: 'Cryptocurrency', name: '加密货币', desc: '管理加密货币操作' }
    ];

    for (const { nodeClass, type, name, desc } of blockchainNodes) {
      this.nodeRegistry.registerNode(
        type,
        nodeClass,
        '区块链',
        desc,
        'link',
        '#FF9800'
      );
      this.registeredNodes.set(type, nodeClass);
    }

    this.registrationStats.blockchainNodes = blockchainNodes.length;
    console.log(`区块链节点注册完成 - ${blockchainNodes.length}个节点`);
  }

  /**
   * 注册学习记录系统节点（5个）
   */
  private async registerLearningRecordNodes(): Promise<void> {
    console.log('注册学习记录系统节点...');

    const learningNodes = [
      { nodeClass: LearningRecordNode, type: 'LearningRecord', name: '学习记录', desc: '创建和管理学习记录' },
      { nodeClass: LearningStatisticsNode, type: 'LearningStatistics', name: '学习统计', desc: '获取用户学习统计信息' },
      { nodeClass: AchievementSystemNode, type: 'AchievementSystem', name: '成就系统', desc: '管理学习成就和奖励' },
      { nodeClass: LearningPathNode, type: 'LearningPath', name: '学习路径', desc: '创建和管理学习路径' },
      { nodeClass: KnowledgeGraphNode, type: 'KnowledgeGraph', name: '知识图谱', desc: '构建和管理知识图谱' }
    ];

    for (const { nodeClass, type, name, desc } of learningNodes) {
      this.nodeRegistry.registerNode(
        type,
        nodeClass,
        '学习记录',
        desc,
        'school',
        '#4CAF50'
      );
      this.registeredNodes.set(type, nodeClass);
    }

    this.registrationStats.learningRecordNodes = learningNodes.length;
    console.log(`学习记录系统节点注册完成 - ${learningNodes.length}个节点`);
  }

  /**
   * 注册RAG应用系统节点（5个）
   */
  private async registerRAGNodes(): Promise<void> {
    console.log('注册RAG应用系统节点...');

    const ragNodes = [
      { nodeClass: KnowledgeBaseNode, type: 'KnowledgeBase', name: '知识库管理', desc: '创建和管理RAG知识库' },
      { nodeClass: RAGQueryNode, type: 'RAGQuery', name: 'RAG查询', desc: '执行检索增强生成查询' },
      { nodeClass: DocumentProcessingNode, type: 'DocumentProcessing', name: '文档处理', desc: '处理和预处理各种格式的文档' },
      { nodeClass: SemanticSearchNode, type: 'SemanticSearch', name: '语义搜索', desc: '执行基于语义相似度的搜索' },
      { nodeClass: DocumentIndexNode, type: 'DocumentIndex', name: '文档索引', desc: '创建和管理文档索引' }
    ];

    for (const { nodeClass, type, name, desc } of ragNodes) {
      this.nodeRegistry.registerNode(
        type,
        nodeClass,
        'RAG应用',
        desc,
        'search',
        '#607D8B'
      );
      this.registeredNodes.set(type, nodeClass);
    }

    this.registrationStats.ragNodes = ragNodes.length;
    console.log(`RAG应用系统节点注册完成 - ${ragNodes.length}个节点`);
  }

  /**
   * 注册协作功能节点（6个）
   */
  private async registerCollaborationNodes(): Promise<void> {
    console.log('注册协作功能节点...');

    const collaborationNodes = [
      { nodeClass: RealTimeCollaborationNode, type: 'RealTimeCollaboration', name: '实时协作', desc: '实现实时多用户协作' },
      { nodeClass: VersionControlNode, type: 'VersionControl', name: '版本控制', desc: '管理项目版本控制' },
      { nodeClass: ConflictResolutionNode, type: 'ConflictResolution', name: '冲突解决', desc: '解决编辑冲突' },
      { nodeClass: PermissionManagementNode, type: 'PermissionManagement', name: '权限管理', desc: '管理用户操作权限' },
      { nodeClass: ActivityTrackingNode, type: 'ActivityTracking', name: '活动追踪', desc: '记录用户操作活动' },
      { nodeClass: NotificationSystemNode, type: 'NotificationSystem', name: '通知系统', desc: '发送系统通知' }
    ];

    for (const { nodeClass, type, name, desc } of collaborationNodes) {
      this.nodeRegistry.registerNode(
        type,
        nodeClass,
        '协作功能',
        desc,
        'group',
        '#9C27B0'
      );
      this.registeredNodes.set(type, nodeClass);
    }

    this.registrationStats.collaborationNodes = collaborationNodes.length;
    console.log(`协作功能节点注册完成 - ${collaborationNodes.length}个节点`);
  }

  /**
   * 注册第三方集成节点（2个）
   */
  private async registerThirdPartyNodes(): Promise<void> {
    console.log('注册第三方集成节点...');

    const thirdPartyNodes = [
      { nodeClass: WebhookIntegrationNode, type: 'WebhookIntegration', name: 'Webhook集成', desc: '集成Webhook服务' },
      { nodeClass: APIGatewayNode, type: 'APIGateway', name: 'API网关', desc: '管理API网关服务' }
    ];

    for (const { nodeClass, type, name, desc } of thirdPartyNodes) {
      this.nodeRegistry.registerNode(
        type,
        nodeClass,
        '第三方集成',
        desc,
        'extension',
        '#FF5722'
      );
      this.registeredNodes.set(type, nodeClass);
    }

    this.registrationStats.thirdPartyNodes = thirdPartyNodes.length;
    console.log(`第三方集成节点注册完成 - ${thirdPartyNodes.length}个节点`);
  }

  /**
   * 计算总体统计信息
   */
  private calculateTotalStats(): void {
    this.registrationStats.totalNodes = 
      this.registrationStats.digitalHumanNodes +
      this.registrationStats.blockchainNodes +
      this.registrationStats.learningRecordNodes +
      this.registrationStats.ragNodes +
      this.registrationStats.collaborationNodes +
      this.registrationStats.thirdPartyNodes;
  }

  /**
   * 获取注册统计信息
   */
  public getRegistrationStats(): any {
    return { ...this.registrationStats };
  }

  /**
   * 获取已注册的节点数量
   */
  public getRegisteredNodeCount(): number {
    return this.registeredNodes.size;
  }

  /**
   * 检查节点是否已注册
   */
  public isNodeRegistered(nodeType: string): boolean {
    return this.registeredNodes.has(nodeType);
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return Array.from(this.registeredNodes.keys());
  }
}

// 创建单例实例
export const batch32To37NodesRegistry = new Batch32To37NodesRegistry();

// 自动注册（可选）
export async function registerBatch32To37Nodes(): Promise<void> {
  await batch32To37NodesRegistry.registerAllNodes();
}

// 导出节点类型常量
export const BATCH_32_TO_37_NODE_TYPES = [
  // 数字人制作系统节点（22个）
  'DigitalHumanEntity', 'DigitalHumanModelLoader', 'DigitalHumanMaterial',
  'DigitalHumanAnimationBinding', 'DigitalHumanPhysics', 'DigitalHumanScenePlacement',
  'FacialExpressionControl', 'EmotionStateManager', 'ExpressionAnimation',
  'ExpressionSync', 'MicroExpression', 'SpeechRecognition', 'SpeechSynthesis',
  'LipSync', 'VoiceEmotion', 'MultiLanguageSupport', 'UserDetection',
  'GreetingBehavior', 'DialogueManager', 'BehaviorResponse',
  'BodyAnimationControl', 'GestureAnimationControl',
  
  // 区块链节点（3个）
  'SmartContract', 'BlockchainTransaction', 'Cryptocurrency',
  
  // 学习记录系统节点（5个）
  'LearningRecord', 'LearningStatistics', 'AchievementSystem',
  'LearningPath', 'KnowledgeGraph',
  
  // RAG应用系统节点（5个）
  'KnowledgeBase', 'RAGQuery', 'DocumentProcessing',
  'SemanticSearch', 'DocumentIndex',
  
  // 协作功能节点（6个）
  'RealTimeCollaboration', 'VersionControl', 'ConflictResolution',
  'PermissionManagement', 'ActivityTracking', 'NotificationSystem',
  
  // 第三方集成节点（2个）
  'WebhookIntegration', 'APIGateway'
] as const;
