/**
 * 语音输入节点
 * 提供语音识别和语音活动检测功能
 */

import { Node } from '../Node';
import { Debug } from '../../../utils/Debug';

/**
 * 语音识别结果接口
 */
export interface SpeechRecognitionResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
  language: string;
  timestamp: number;
}

/**
 * 语音活动检测结果接口
 */
export interface VoiceActivityResult {
  isActive: boolean;
  audioLevel: number;
  duration: number;
  timestamp: number;
}

/**
 * 语音识别节点
 * 将语音转换为文本
 */
export class SpeechRecognitionNode extends Node {
  public static readonly TYPE = 'SpeechRecognitionNode';
  public static readonly NAME = '语音识别节点';
  public static readonly DESCRIPTION = '将语音转换为文本，支持多种语言和连续识别';

  private recognition: any = null;
  private isListening: boolean = false;
  private currentLanguage: string = 'zh-CN';

  constructor(type: string = SpeechRecognitionNode.TYPE, name: string = SpeechRecognitionNode.NAME, id?: string) {
    super(type, name, id);
    
    // 输入端口
    this.addInput('enable', 'boolean', '启用', true);
    this.addInput('language', 'string', '语言', 'zh-CN');
    this.addInput('continuous', 'boolean', '连续识别', true);
    this.addInput('interimResults', 'boolean', '中间结果', true);
    this.addInput('maxAlternatives', 'number', '最大候选数', 1);
    this.addInput('confidenceThreshold', 'number', '置信度阈值', 0.5);

    // 输出端口
    this.addOutput('transcript', 'string', '识别文本');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('isFinal', 'boolean', '是否最终结果');
    this.addOutput('language', 'string', '识别语言');
    this.addOutput('alternatives', 'array', '候选结果');
    this.addOutput('isListening', 'boolean', '是否正在监听');
    this.addOutput('onResult', 'event', '识别结果事件');
    this.addOutput('onStart', 'event', '开始识别事件');
    this.addOutput('onEnd', 'event', '结束识别事件');
    this.addOutput('onError', 'event', '错误事件');

    this.initializeSpeechRecognition();
  }

  /**
   * 初始化语音识别
   */
  private initializeSpeechRecognition(): void {
    try {
      // 检查浏览器支持
      if (typeof window !== 'undefined' && 'webkitSpeechRecognition' in window) {
        this.recognition = new (window as any).webkitSpeechRecognition();
      } else if (typeof window !== 'undefined' && 'SpeechRecognition' in window) {
        this.recognition = new (window as any).SpeechRecognition();
      } else {
        Debug.warn('SpeechRecognitionNode', '浏览器不支持语音识别API');
        return;
      }

      // 配置语音识别
      this.recognition.continuous = true;
      this.recognition.interimResults = true;
      this.recognition.lang = this.currentLanguage;
      this.recognition.maxAlternatives = 1;

      // 绑定事件
      this.setupRecognitionEvents();

    } catch (error) {
      Debug.error('SpeechRecognitionNode', '语音识别初始化失败', error);
    }
  }

  /**
   * 设置语音识别事件
   */
  private setupRecognitionEvents(): void {
    if (!this.recognition) return;

    this.recognition.onstart = () => {
      this.isListening = true;
      Debug.log('SpeechRecognitionNode', '开始语音识别');
    };

    this.recognition.onend = () => {
      this.isListening = false;
      Debug.log('SpeechRecognitionNode', '语音识别结束');
    };

    this.recognition.onresult = (event: any) => {
      this.handleRecognitionResult(event);
    };

    this.recognition.onerror = (event: any) => {
      Debug.error('SpeechRecognitionNode', '语音识别错误', event.error);
    };
  }

  /**
   * 处理识别结果
   */
  private handleRecognitionResult(event: any): void {
    try {
      const results = event.results;
      const lastResult = results[results.length - 1];
      
      if (lastResult) {
        const transcript = lastResult[0].transcript;
        const confidence = lastResult[0].confidence || 0;
        const isFinal = lastResult.isFinal;

        const result: SpeechRecognitionResult = {
          transcript,
          confidence,
          isFinal,
          language: this.currentLanguage,
          timestamp: Date.now()
        };

        // 触发结果事件
        this.emit('speechResult', result);
      }

    } catch (error) {
      Debug.error('SpeechRecognitionNode', '处理识别结果失败', error);
    }
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const language = inputs?.language as string || 'zh-CN';
      const continuous = inputs?.continuous !== false;
      const interimResults = inputs?.interimResults !== false;
      const maxAlternatives = inputs?.maxAlternatives as number || 1;
      const confidenceThreshold = inputs?.confidenceThreshold as number || 0.5;

      if (!enable) {
        this.stopRecognition();
        return this.getDefaultOutputs();
      }

      // 更新配置
      if (this.recognition) {
        this.recognition.lang = language;
        this.recognition.continuous = continuous;
        this.recognition.interimResults = interimResults;
        this.recognition.maxAlternatives = maxAlternatives;
        this.currentLanguage = language;
      }

      // 开始识别
      this.startRecognition();

      // 模拟识别结果（用于演示）
      const mockResult = this.generateMockResult(confidenceThreshold);

      return {
        transcript: mockResult.transcript,
        confidence: mockResult.confidence,
        isFinal: mockResult.isFinal,
        language: mockResult.language,
        alternatives: [mockResult],
        isListening: this.isListening,
        onResult: mockResult.isFinal,
        onStart: this.isListening,
        onEnd: !this.isListening,
        onError: false
      };

    } catch (error) {
      Debug.error('SpeechRecognitionNode', '语音识别执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  /**
   * 开始语音识别
   */
  private startRecognition(): void {
    if (this.recognition && !this.isListening) {
      try {
        this.recognition.start();
      } catch (error) {
        Debug.error('SpeechRecognitionNode', '启动语音识别失败', error);
      }
    }
  }

  /**
   * 停止语音识别
   */
  private stopRecognition(): void {
    if (this.recognition && this.isListening) {
      try {
        this.recognition.stop();
      } catch (error) {
        Debug.error('SpeechRecognitionNode', '停止语音识别失败', error);
      }
    }
  }

  /**
   * 生成模拟识别结果
   */
  private generateMockResult(confidenceThreshold: number): SpeechRecognitionResult {
    const mockTexts = ['你好', '开始', '停止', '确认', '取消', '帮助'];
    const transcript = mockTexts[Math.floor(Math.random() * mockTexts.length)];
    const confidence = confidenceThreshold + Math.random() * (1 - confidenceThreshold);
    
    return {
      transcript,
      confidence,
      isFinal: Math.random() > 0.3,
      language: this.currentLanguage,
      timestamp: Date.now()
    };
  }

  private getDefaultOutputs(): any {
    return {
      transcript: '',
      confidence: 0,
      isFinal: false,
      language: this.currentLanguage,
      alternatives: [],
      isListening: false,
      onResult: false,
      onStart: false,
      onEnd: false,
      onError: false
    };
  }
}

/**
 * 语音活动检测节点
 * 检测语音活动状态
 */
export class VoiceActivityDetectionNode extends Node {
  public static readonly TYPE = 'VoiceActivityDetectionNode';
  public static readonly NAME = '语音活动检测节点';
  public static readonly DESCRIPTION = '检测语音活动状态，识别说话和静音时段';

  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private microphone: MediaStreamAudioSourceNode | null = null;
  private isMonitoring: boolean = false;

  constructor(type: string = VoiceActivityDetectionNode.TYPE, name: string = VoiceActivityDetectionNode.NAME, id?: string) {
    super(type, name, id);
    
    // 输入端口
    this.addInput('enable', 'boolean', '启用', true);
    this.addInput('threshold', 'number', '活动阈值', 0.01);
    this.addInput('smoothing', 'number', '平滑度', 0.8);
    this.addInput('minDuration', 'number', '最小持续时间(ms)', 100);

    // 输出端口
    this.addOutput('isActive', 'boolean', '是否活跃');
    this.addOutput('audioLevel', 'number', '音频电平');
    this.addOutput('duration', 'number', '活动持续时间');
    this.addOutput('timestamp', 'number', '时间戳');
    this.addOutput('onSpeechStart', 'event', '开始说话事件');
    this.addOutput('onSpeechEnd', 'event', '停止说话事件');
    this.addOutput('onSilence', 'event', '静音事件');

    this.initializeAudioContext();
  }

  /**
   * 初始化音频上下文
   */
  private async initializeAudioContext(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && 'AudioContext' in window) {
        this.audioContext = new AudioContext();
        this.analyser = this.audioContext.createAnalyser();
        this.analyser.fftSize = 256;
        this.analyser.smoothingTimeConstant = 0.8;
      }
    } catch (error) {
      Debug.error('VoiceActivityDetectionNode', '音频上下文初始化失败', error);
    }
  }

  /**
   * 开始监听麦克风
   */
  private async startMonitoring(): Promise<void> {
    try {
      if (!this.audioContext || !this.analyser) return;

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      this.microphone = this.audioContext.createMediaStreamSource(stream);
      this.microphone.connect(this.analyser);
      this.isMonitoring = true;

      Debug.log('VoiceActivityDetectionNode', '开始语音活动监听');

    } catch (error) {
      Debug.error('VoiceActivityDetectionNode', '启动麦克风监听失败', error);
    }
  }

  /**
   * 停止监听
   */
  private stopMonitoring(): void {
    if (this.microphone) {
      this.microphone.disconnect();
      this.microphone = null;
    }
    this.isMonitoring = false;
    Debug.log('VoiceActivityDetectionNode', '停止语音活动监听');
  }

  /**
   * 分析音频电平
   */
  private analyzeAudioLevel(): number {
    if (!this.analyser) return 0;

    const bufferLength = this.analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    this.analyser.getByteFrequencyData(dataArray);

    let sum = 0;
    for (let i = 0; i < bufferLength; i++) {
      sum += dataArray[i];
    }

    return sum / bufferLength / 255; // 归一化到0-1
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const threshold = inputs?.threshold as number || 0.01;
      const smoothing = inputs?.smoothing as number || 0.8;
      const minDuration = inputs?.minDuration as number || 100;

      if (!enable) {
        this.stopMonitoring();
        return this.getDefaultOutputs();
      }

      // 开始监听（如果尚未开始）
      if (!this.isMonitoring) {
        this.startMonitoring();
      }

      // 分析音频电平
      const audioLevel = this.analyzeAudioLevel();
      const isActive = audioLevel > threshold;
      const timestamp = Date.now();

      // 模拟语音活动检测结果
      const mockResult = this.generateMockVADResult(threshold);

      return {
        isActive: mockResult.isActive,
        audioLevel: mockResult.audioLevel,
        duration: mockResult.duration,
        timestamp: mockResult.timestamp,
        onSpeechStart: mockResult.isActive && !this.isMonitoring,
        onSpeechEnd: !mockResult.isActive && this.isMonitoring,
        onSilence: !mockResult.isActive
      };

    } catch (error) {
      Debug.error('VoiceActivityDetectionNode', '语音活动检测执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  /**
   * 生成模拟VAD结果
   */
  private generateMockVADResult(threshold: number): VoiceActivityResult {
    const audioLevel = Math.random();
    const isActive = audioLevel > threshold;
    
    return {
      isActive,
      audioLevel,
      duration: isActive ? Math.random() * 2000 + 500 : 0,
      timestamp: Date.now()
    };
  }

  private getDefaultOutputs(): any {
    return {
      isActive: false,
      audioLevel: 0,
      duration: 0,
      timestamp: 0,
      onSpeechStart: false,
      onSpeechEnd: false,
      onSilence: true
    };
  }

  public dispose(): void {
    this.stopMonitoring();
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    super.dispose();
  }
}
