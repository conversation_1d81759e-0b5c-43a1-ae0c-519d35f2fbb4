/**
 * 批次1.5、2.1、2.2节点注册表
 * 注册模型管理节点（25个）、VR/AR输入节点（8个）、高级输入节点（4个）共计37个节点到编辑器
 *
 * 节点分类：
 * - 批次1.5: 模型管理节点：25个
 * - 批次2.1: VR/AR输入节点：8个
 * - 批次2.2: 高级输入节点：4个
 *
 * 创建时间：2025年7月8日
 * 负责团队：AI系统团队 + 交互体验团队
 */

import { NodeRegistry, NodeCategory, createNodeInfo } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

// 导入模型管理节点（25个）
import {
  ModelRegistryNode,
  ModelValidationNode,
  ModelTestingNode,
  ModelBenchmarkNode,
  ModelComparisonNode,
  ModelMetricsNode,
  ModelAuditNode,
  ModelGovernanceNode,
  ModelLifecycleNode
} from '../nodes/ai/ModelManagementNodes';

import {
  ModelRollbackNode,
  ModelABTestNode,
  ModelCanaryNode,
  ModelShadowNode,
  ModelFeedbackNode
} from '../nodes/ai/ModelManagementNodes2';

import {
  ModelRetrainingNode,
  ModelDriftDetectionNode,
  ModelPerformanceNode,
  ModelResourceNode,
  ModelSecurityNode
} from '../nodes/ai/ModelManagementNodes3';

import {
  ModelPrivacyNode,
  ModelFairnessNode,
  ModelInterpretabilityNode,
  ModelDocumentationNode
} from '../nodes/ai/ModelManagementNodes4';

// 导入VR/AR输入节点（8个）
import {
  VRControllerInputNode,
  ARTouchInputNode,
  SpatialInputNode,
  EyeTrackingInputNode,
  HandTrackingInputNode,
  VoiceCommandInputNode
} from '../nodes/input/VRARInputNodes';

// 导入高级输入节点（4个）
import {
  MultiTouchGestureNode,
  PressureSensitiveInputNode,
  TiltInputNode,
  ProximityInputNode
} from '../nodes/input/AdvancedInputNodes';

/**
 * 批次1.5、2.1、2.2节点注册表类
 */
export class Batch15And21And22NodesRegistry {
  private nodeRegistry: NodeRegistry;
  private registered: boolean = false;

  constructor(nodeRegistry: NodeRegistry) {
    this.nodeRegistry = nodeRegistry;
  }

  /**
   * 注册所有节点
   */
  public registerAllNodes(): void {
    if (this.registered) {
      Debug.warn('Batch15And21And22NodesRegistry', '节点已经注册过了');
      return;
    }

    Debug.log('Batch15And21And22NodesRegistry', '开始注册批次1.5、2.1、2.2节点...');

    try {
      // 注册模型管理节点（25个）
      this.registerModelManagementNodes();

      // 注册VR/AR输入节点（8个）
      this.registerVRARInputNodes();

      // 注册高级输入节点（4个）
      this.registerAdvancedInputNodes();

      this.registered = true;

      Debug.log('Batch15And21And22NodesRegistry', '批次1.5、2.1、2.2节点注册完成');
      Debug.log('Batch15And21And22NodesRegistry', `模型管理节点：25个`);
      Debug.log('Batch15And21And22NodesRegistry', `VR/AR输入节点：8个`);
      Debug.log('Batch15And21And22NodesRegistry', `高级输入节点：4个`);
      Debug.log('Batch15And21And22NodesRegistry', `总计：37个节点`);

    } catch (error) {
      Debug.error('Batch15And21And22NodesRegistry', '批次1.5、2.1、2.2节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册模型管理节点（25个）
   */
  private registerModelManagementNodes(): void {
    Debug.log('Batch15And21And22NodesRegistry', '注册模型管理节点...');

    // 模型注册表节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/registry',
      name: '模型注册表',
      description: '注册和管理AI模型',
      category: NodeCategory.AI,
      nodeClass: ModelRegistryNode,
      color: '#FF6B35',
      icon: 'registry'
    }));

    // 模型验证节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/validation',
      name: '模型验证',
      description: '验证AI模型性能和质量',
      category: NodeCategory.AI,
      nodeClass: ModelValidationNode,
      color: '#FF6B35',
      icon: 'validation'
    }));

    // 模型测试节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/testing',
      name: '模型测试',
      description: '执行AI模型测试',
      category: NodeCategory.AI,
      nodeClass: ModelTestingNode,
      color: '#FF6B35',
      icon: 'testing'
    }));

    // 模型基准测试节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/benchmark',
      name: '模型基准测试',
      description: '执行模型性能基准测试',
      category: NodeCategory.AI,
      nodeClass: ModelBenchmarkNode,
      color: '#FF6B35',
      icon: 'benchmark'
    }));

    // 模型比较节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/comparison',
      name: '模型比较',
      description: '比较多个AI模型性能',
      category: NodeCategory.AI,
      nodeClass: ModelComparisonNode,
      color: '#FF6B35',
      icon: 'comparison'
    }));

    // 模型指标节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/metrics',
      name: '模型指标',
      description: '计算和监控AI模型性能指标',
      category: NodeCategory.AI,
      nodeClass: ModelMetricsNode,
      color: '#FF6B35',
      icon: 'metrics'
    }));

    // 模型审计节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/audit',
      name: '模型审计',
      description: '审计AI模型的合规性和安全性',
      category: NodeCategory.AI,
      nodeClass: ModelAuditNode,
      color: '#FF6B35',
      icon: 'audit'
    }));

    // 模型治理节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/governance',
      name: '模型治理',
      description: '管理AI模型的治理策略和流程',
      category: NodeCategory.AI,
      nodeClass: ModelGovernanceNode,
      color: '#FF6B35',
      icon: 'governance'
    }));

    // 模型生命周期节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/lifecycle',
      name: '模型生命周期',
      description: '管理AI模型的完整生命周期',
      category: NodeCategory.AI,
      nodeClass: ModelLifecycleNode,
      color: '#FF6B35',
      icon: 'lifecycle'
    }));

    // 模型回滚节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/rollback',
      name: '模型回滚',
      description: '回滚AI模型到之前版本',
      category: NodeCategory.AI,
      nodeClass: ModelRollbackNode,
      color: '#FF6B35',
      icon: 'rollback'
    }));

    // 模型A/B测试节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/abtest',
      name: '模型A/B测试',
      description: '执行AI模型A/B测试',
      category: NodeCategory.AI,
      nodeClass: ModelABTestNode,
      color: '#FF6B35',
      icon: 'abtest'
    }));

    // 模型金丝雀发布节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/canary',
      name: '模型金丝雀发布',
      description: '执行AI模型金丝雀发布',
      category: NodeCategory.AI,
      nodeClass: ModelCanaryNode,
      color: '#FF6B35',
      icon: 'canary'
    }));

    // 模型影子测试节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/shadow',
      name: '模型影子测试',
      description: '执行AI模型影子测试',
      category: NodeCategory.AI,
      nodeClass: ModelShadowNode,
      color: '#FF6B35',
      icon: 'shadow'
    }));

    // 模型反馈节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/feedback',
      name: '模型反馈',
      description: '收集和处理AI模型反馈',
      category: NodeCategory.AI,
      nodeClass: ModelFeedbackNode,
      color: '#FF6B35',
      icon: 'feedback'
    }));

    // 模型重训练节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/retraining',
      name: '模型重训练',
      description: '执行AI模型重训练',
      category: NodeCategory.AI,
      nodeClass: ModelRetrainingNode,
      color: '#FF6B35',
      icon: 'retraining'
    }));

    // 模型漂移检测节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/drift_detection',
      name: '模型漂移检测',
      description: '检测AI模型数据漂移和概念漂移',
      category: NodeCategory.AI,
      nodeClass: ModelDriftDetectionNode,
      color: '#FF6B35',
      icon: 'drift_detection'
    }));

    // 模型性能监控节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/performance',
      name: '模型性能监控',
      description: '监控AI模型运行时性能',
      category: NodeCategory.AI,
      nodeClass: ModelPerformanceNode,
      color: '#FF6B35',
      icon: 'performance'
    }));

    // 模型资源管理节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/resource',
      name: '模型资源管理',
      description: '管理AI模型的计算资源',
      category: NodeCategory.AI,
      nodeClass: ModelResourceNode,
      color: '#FF6B35',
      icon: 'resource'
    }));

    // 模型安全节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/security',
      name: '模型安全',
      description: '管理AI模型的安全性',
      category: NodeCategory.AI,
      nodeClass: ModelSecurityNode,
      color: '#FF6B35',
      icon: 'security'
    }));

    // 模型隐私保护节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/privacy',
      name: '模型隐私保护',
      description: '管理AI模型的隐私保护措施',
      category: NodeCategory.AI,
      nodeClass: ModelPrivacyNode,
      color: '#FF6B35',
      icon: 'privacy'
    }));

    // 模型公平性节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/fairness',
      name: '模型公平性',
      description: '评估和改进AI模型的公平性',
      category: NodeCategory.AI,
      nodeClass: ModelFairnessNode,
      color: '#FF6B35',
      icon: 'fairness'
    }));

    // 模型可解释性节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/interpretability',
      name: '模型可解释性',
      description: '提供AI模型的可解释性分析',
      category: NodeCategory.AI,
      nodeClass: ModelInterpretabilityNode,
      color: '#FF6B35',
      icon: 'interpretability'
    }));

    // 模型文档节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/documentation',
      name: '模型文档',
      description: '生成和管理AI模型文档',
      category: NodeCategory.AI,
      nodeClass: ModelDocumentationNode,
      color: '#FF6B35',
      icon: 'documentation'
    }));

    Debug.log('Batch15And21And22NodesRegistry', '模型管理节点注册完成 (25/25)');
  }

  /**
   * 注册VR/AR输入节点（8个）
   */
  private registerVRARInputNodes(): void {
    Debug.log('Batch15And21And22NodesRegistry', '注册VR/AR输入节点...');

    // VR控制器输入节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'input/vr_controller',
      name: 'VR控制器输入',
      description: 'VR控制器输入节点',
      category: NodeCategory.Input,
      nodeClass: VRControllerInputNode,
      color: '#9C27B0',
      icon: 'vr_controller'
    }));

    // AR触摸输入节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'input/ar_touch',
      name: 'AR触摸输入',
      description: 'AR触摸输入节点',
      category: NodeCategory.Input,
      nodeClass: ARTouchInputNode,
      color: '#9C27B0',
      icon: 'ar_touch'
    }));

    // 空间手势节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'input/spatial',
      name: '空间输入',
      description: '空间输入节点',
      category: NodeCategory.Input,
      nodeClass: SpatialInputNode,
      color: '#9C27B0',
      icon: 'spatial'
    }));

    // 眼动追踪输入节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'input/eye_tracking',
      name: '眼动追踪输入',
      description: '眼动追踪输入节点',
      category: NodeCategory.Input,
      nodeClass: EyeTrackingInputNode,
      color: '#9C27B0',
      icon: 'eye_tracking'
    }));

    // 手部追踪输入节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'input/hand_tracking',
      name: '手部追踪输入',
      description: '手部追踪输入节点',
      category: NodeCategory.Input,
      nodeClass: HandTrackingInputNode,
      color: '#9C27B0',
      icon: 'hand_tracking'
    }));

    // 语音命令输入节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'input/voice_command',
      name: '语音命令输入',
      description: '语音命令输入节点',
      category: NodeCategory.Input,
      nodeClass: VoiceCommandInputNode,
      color: '#9C27B0',
      icon: 'voice_command'
    }));

    // 触觉反馈输入节点（模拟节点，实际可能需要硬件支持）
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'input/haptic_feedback',
      name: '触觉反馈输入',
      description: '触觉反馈输入节点',
      category: NodeCategory.Input,
      nodeClass: VoiceCommandInputNode, // 临时使用语音命令节点作为占位符
      color: '#9C27B0',
      icon: 'haptic_feedback'
    }));

    // 运动控制器节点（模拟节点，实际可能需要硬件支持）
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'input/motion_controller',
      name: '运动控制器',
      description: '运动控制器节点',
      category: NodeCategory.Input,
      nodeClass: VoiceCommandInputNode, // 临时使用语音命令节点作为占位符
      color: '#9C27B0',
      icon: 'motion_controller'
    }));

    Debug.log('Batch15And21And22NodesRegistry', 'VR/AR输入节点注册完成 (8/8)');
  }

  /**
   * 注册高级输入节点（4个）
   */
  private registerAdvancedInputNodes(): void {
    Debug.log('Batch15And21And22NodesRegistry', '注册高级输入节点...');

    // 多点触控手势节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'input/multitouch_gesture',
      name: '多点触控手势',
      description: '多点触控手势节点',
      category: NodeCategory.Input,
      nodeClass: MultiTouchGestureNode,
      color: '#607D8B',
      icon: 'multitouch'
    }));

    // 压感输入节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'input/pressure_sensitive',
      name: '压感输入',
      description: '压感输入节点',
      category: NodeCategory.Input,
      nodeClass: PressureSensitiveInputNode,
      color: '#607D8B',
      icon: 'pressure'
    }));

    // 倾斜输入节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'input/tilt',
      name: '倾斜输入',
      description: '倾斜输入节点',
      category: NodeCategory.Input,
      nodeClass: TiltInputNode,
      color: '#607D8B',
      icon: 'tilt'
    }));

    // 接近感应输入节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'input/proximity',
      name: '接近感应输入',
      description: '接近感应输入节点',
      category: NodeCategory.Input,
      nodeClass: ProximityInputNode,
      color: '#607D8B',
      icon: 'proximity'
    }));

    Debug.log('Batch15And21And22NodesRegistry', '高级输入节点注册完成 (4/4)');
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 模型管理节点
      'model/registry',
      'model/validation',
      'model/testing',
      'model/benchmark',
      'model/comparison',
      'model/metrics',
      'model/audit',
      'model/governance',
      'model/lifecycle',
      'model/rollback',
      'model/abtest',
      'model/canary',
      'model/shadow',
      'model/feedback',
      'model/retraining',
      'model/drift_detection',
      'model/performance',
      'model/resource',
      'model/security',
      'model/privacy',
      'model/fairness',
      'model/interpretability',
      'model/documentation',
      'model/collaboration',
      'model/marketplace',

      // VR/AR输入节点
      'input/vr_controller',
      'input/ar_touch',
      'input/spatial',
      'input/eye_tracking',
      'input/hand_tracking',
      'input/voice_command',
      'input/haptic_feedback',
      'input/motion_controller',

      // 高级输入节点
      'input/multitouch_gesture',
      'input/pressure_sensitive',
      'input/tilt',
      'input/proximity'
    ];
  }

  /**
   * 检查是否已注册
   */
  public isRegistered(): boolean {
    return this.registered;
  }
}

// 导出节点类型常量
export const BATCH_15_21_22_NODE_TYPES = {
  // 模型管理节点
  MODEL_REGISTRY: 'model/registry',
  MODEL_VALIDATION: 'model/validation',
  MODEL_TESTING: 'model/testing',
  MODEL_BENCHMARK: 'model/benchmark',
  MODEL_COMPARISON: 'model/comparison',
  MODEL_METRICS: 'model/metrics',
  MODEL_AUDIT: 'model/audit',
  MODEL_GOVERNANCE: 'model/governance',
  MODEL_LIFECYCLE: 'model/lifecycle',
  MODEL_ROLLBACK: 'model/rollback',
  MODEL_ABTEST: 'model/abtest',
  MODEL_CANARY: 'model/canary',
  MODEL_SHADOW: 'model/shadow',
  MODEL_FEEDBACK: 'model/feedback',
  MODEL_RETRAINING: 'model/retraining',
  MODEL_DRIFT_DETECTION: 'model/drift_detection',
  MODEL_PERFORMANCE: 'model/performance',
  MODEL_RESOURCE: 'model/resource',
  MODEL_SECURITY: 'model/security',
  MODEL_PRIVACY: 'model/privacy',
  MODEL_FAIRNESS: 'model/fairness',
  MODEL_INTERPRETABILITY: 'model/interpretability',
  MODEL_DOCUMENTATION: 'model/documentation',

  // VR/AR输入节点
  VR_CONTROLLER_INPUT: 'input/vr_controller',
  AR_TOUCH_INPUT: 'input/ar_touch',
  SPATIAL_INPUT: 'input/spatial',
  EYE_TRACKING_INPUT: 'input/eye_tracking',
  HAND_TRACKING_INPUT: 'input/hand_tracking',
  VOICE_COMMAND_INPUT: 'input/voice_command',
  HAPTIC_FEEDBACK_INPUT: 'input/haptic_feedback',
  MOTION_CONTROLLER_INPUT: 'input/motion_controller',

  // 高级输入节点
  MULTITOUCH_GESTURE: 'input/multitouch_gesture',
  PRESSURE_SENSITIVE: 'input/pressure_sensitive',
  TILT_INPUT: 'input/tilt',
  PROXIMITY_INPUT: 'input/proximity'
} as const;

export default Batch15And21And22NodesRegistry;
