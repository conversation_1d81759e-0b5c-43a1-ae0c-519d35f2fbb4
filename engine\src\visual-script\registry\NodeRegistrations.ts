/**
 * 节点注册实现
 * 负责具体的节点类导入和注册
 */
import { NodeRegistry, NodeCategory, createNodeInfo, registerNodes } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

/**
 * 注册所有可用的节点
 * 这个函数应该在应用启动时调用
 */
export async function registerAllAvailableNodes(): Promise<void> {
  Debug.log('NodeRegistrations', '开始注册所有可用节点...');

  try {
    // 注册核心节点
    await registerCoreNodes();
    
    // 注册渲染节点
    await registerRenderingNodes();
    
    // 注册物理节点
    await registerPhysicsNodes();
    
    // 注册动画节点
    await registerAnimationNodes();
    
    // 注册音频节点
    await registerAudioNodes();
    
    // 注册输入节点
    await registerInputNodes();
    
    // 注册网络节点
    await registerNetworkNodes();
    
    // 注册AI节点
    await registerAINodes();
    
    // 注册场景节点
    await registerSceneNodes();
    
    // 注册资源节点
    await registerResourceNodes();
    
    // 注册工业节点
    await registerIndustrialNodes();
    
    // 注册边缘计算节点
    await registerEdgeComputingNodes();
    
    // 注册专业应用节点
    await registerSpecializedNodes();
    
    // 注册数据服务节点
    await registerDataServiceNodes();
    
    // 注册UI节点
    await registerUINodes();
    
    // 注册动作捕捉节点
    await registerMotionCaptureNodes();

    // 注册批次3：高级渲染效果节点
    await registerAdvancedRenderingEffectsNodes();

    // 注册批次6：服务器与云端节点
    await registerServerCloudNodes();

    // 注册批次7：边缘计算与5G节点
    await registerEdgeComputing5GNodes();

    // 注册批次8：交互体验系统节点
    await registerInteractionExperienceNodes();

    // 注册批次9：专业应用领域节点
    await registerProfessionalApplicationNodes();

    // 注册批次10：内容创作与输入节点
    await registerContentCreationInputNodes();

    // 注册批次12：物理与动画系统节点
    await registerPhysicsAnimationNodes();

    // 注册批次1.5、2.1、2.2：模型管理、VR/AR输入、高级输入节点
    await registerBatch15And21And22Nodes();

    Debug.log('NodeRegistrations', '所有节点注册完成');
  } catch (error) {
    Debug.error('NodeRegistrations', '节点注册过程中发生错误:', error);
  }
}

/**
 * 注册核心节点
 */
async function registerCoreNodes(): Promise<void> {
  try {
    // 动态导入核心节点类
    const coreNodes = [];

    // 实体管理节点
    try {
      const { CreateEntityNode, DestroyEntityNode, FindEntityNode, CloneEntityNode, EntityActiveNode } = 
        await import('../nodes/entity/EntityNodes');
      
      coreNodes.push(
        createNodeInfo('CreateEntity', '创建实体', '在场景中创建新的实体对象', NodeCategory.ENTITY_MANAGEMENT, CreateEntityNode, {
          icon: 'add_box',
          color: '#4ECDC4',
          tags: ['entity', 'create', 'object']
        }),
        createNodeInfo('DestroyEntity', '销毁实体', '从场景中销毁指定的实体对象', NodeCategory.ENTITY_MANAGEMENT, DestroyEntityNode, {
          icon: 'delete',
          color: '#4ECDC4',
          tags: ['entity', 'destroy', 'remove']
        }),
        createNodeInfo('FindEntity', '查找实体', '根据名称或ID查找场景中的实体', NodeCategory.ENTITY_MANAGEMENT, FindEntityNode, {
          icon: 'search',
          color: '#4ECDC4',
          tags: ['entity', 'find', 'query']
        }),
        createNodeInfo('CloneEntity', '克隆实体', '复制现有实体创建新的实体', NodeCategory.ENTITY_MANAGEMENT, CloneEntityNode, {
          icon: 'content_copy',
          color: '#4ECDC4',
          tags: ['entity', 'clone', 'duplicate']
        }),
        createNodeInfo('EntityActive', '实体激活状态', '获取或设置实体的激活状态', NodeCategory.ENTITY_MANAGEMENT, EntityActiveNode, {
          icon: 'visibility',
          color: '#4ECDC4',
          tags: ['entity', 'active', 'status']
        })
      );
    } catch (error) {
      Debug.warn('NodeRegistrations', '实体节点导入失败:', error);
    }

    // 组件管理节点
    try {
      const { AddComponentNode, RemoveComponentNode, GetComponentNode, ComponentEnabledNode, GetAllComponentsNode, ComponentPropertyNode } = 
        await import('../nodes/entity/ComponentNodes');
      
      coreNodes.push(
        createNodeInfo('AddComponent', '添加组件', '为实体添加指定类型的组件', NodeCategory.COMPONENT_MANAGEMENT, AddComponentNode, {
          icon: 'add_circle',
          color: '#45B7D1',
          tags: ['component', 'add', 'attach']
        }),
        createNodeInfo('RemoveComponent', '移除组件', '从实体中移除指定类型的组件', NodeCategory.COMPONENT_MANAGEMENT, RemoveComponentNode, {
          icon: 'remove_circle',
          color: '#45B7D1',
          tags: ['component', 'remove', 'detach']
        }),
        createNodeInfo('GetComponent', '获取组件', '获取实体上指定类型的组件', NodeCategory.COMPONENT_MANAGEMENT, GetComponentNode, {
          icon: 'get_app',
          color: '#45B7D1',
          tags: ['component', 'get', 'retrieve']
        }),
        createNodeInfo('ComponentEnabled', '组件启用状态', '获取或设置组件的启用状态', NodeCategory.COMPONENT_MANAGEMENT, ComponentEnabledNode, {
          icon: 'toggle_on',
          color: '#45B7D1',
          tags: ['component', 'enabled', 'toggle']
        }),
        createNodeInfo('GetAllComponents', '获取所有组件', '获取实体上的所有组件', NodeCategory.COMPONENT_MANAGEMENT, GetAllComponentsNode, {
          icon: 'list',
          color: '#45B7D1',
          tags: ['component', 'all', 'list']
        }),
        createNodeInfo('ComponentProperty', '组件属性', '获取或设置组件的属性值', NodeCategory.COMPONENT_MANAGEMENT, ComponentPropertyNode, {
          icon: 'settings',
          color: '#45B7D1',
          tags: ['component', 'property', 'attribute']
        })
      );
    } catch (error) {
      Debug.warn('NodeRegistrations', '组件节点导入失败:', error);
    }

    // 变换节点
    try {
      const { SetPositionNode, GetPositionNode, SetRotationNode, GetRotationNode, SetScaleNode, GetScaleNode, MoveNode, RotateNode } = 
        await import('../nodes/entity/TransformNodes');
      
      coreNodes.push(
        createNodeInfo('SetPosition', '设置位置', '设置实体的世界坐标位置', NodeCategory.TRANSFORM, SetPositionNode, {
          icon: 'place',
          color: '#96CEB4',
          tags: ['transform', 'position', 'location']
        }),
        createNodeInfo('GetPosition', '获取位置', '获取实体的世界坐标位置', NodeCategory.TRANSFORM, GetPositionNode, {
          icon: 'my_location',
          color: '#96CEB4',
          tags: ['transform', 'position', 'location']
        }),
        createNodeInfo('SetRotation', '设置旋转', '设置实体的旋转角度', NodeCategory.TRANSFORM, SetRotationNode, {
          icon: 'rotate_right',
          color: '#96CEB4',
          tags: ['transform', 'rotation', 'angle']
        }),
        createNodeInfo('GetRotation', '获取旋转', '获取实体的旋转角度', NodeCategory.TRANSFORM, GetRotationNode, {
          icon: 'rotate_left',
          color: '#96CEB4',
          tags: ['transform', 'rotation', 'angle']
        }),
        createNodeInfo('SetScale', '设置缩放', '设置实体的缩放比例', NodeCategory.TRANSFORM, SetScaleNode, {
          icon: 'zoom_in',
          color: '#96CEB4',
          tags: ['transform', 'scale', 'size']
        }),
        createNodeInfo('GetScale', '获取缩放', '获取实体的缩放比例', NodeCategory.TRANSFORM, GetScaleNode, {
          icon: 'zoom_out',
          color: '#96CEB4',
          tags: ['transform', 'scale', 'size']
        }),
        createNodeInfo('Move', '移动', '相对移动实体位置', NodeCategory.TRANSFORM, MoveNode, {
          icon: 'open_with',
          color: '#96CEB4',
          tags: ['transform', 'move', 'translate']
        }),
        createNodeInfo('Rotate', '旋转', '相对旋转实体', NodeCategory.TRANSFORM, RotateNode, {
          icon: '3d_rotation',
          color: '#96CEB4',
          tags: ['transform', 'rotate', 'spin']
        })
      );
    } catch (error) {
      Debug.warn('NodeRegistrations', '变换节点导入失败:', error);
    }

    // 注册所有核心节点
    if (coreNodes.length > 0) {
      registerNodes(coreNodes);
      Debug.log('NodeRegistrations', `核心节点注册完成: ${coreNodes.length}个节点`);
    }
  } catch (error) {
    Debug.error('NodeRegistrations', '核心节点注册失败:', error);
  }
}

/**
 * 注册渲染节点
 */
async function registerRenderingNodes(): Promise<void> {
  try {
    // 注册批次1：核心渲染系统节点（60个）
    try {
      const { coreRenderingNodesRegistry } = await import('./CoreRenderingNodesRegistry');
      coreRenderingNodesRegistry.registerAllNodes();
      Debug.log('NodeRegistrations', '核心渲染系统节点注册完成: 60个节点');
    } catch (error) {
      Debug.error('NodeRegistrations', '核心渲染系统节点注册失败:', error);
    }

    // 注册其他渲染节点
    const renderingNodes = [];

    // 基础渲染节点
    try {
      const { RenderConfigNode } = await import('../nodes/rendering/RenderingNodes');

      renderingNodes.push(
        createNodeInfo('RenderConfig', '渲染配置', '配置渲染管线和质量设置', NodeCategory.RENDERING, RenderConfigNode, {
          icon: 'settings',
          color: '#E67E22',
          tags: ['render', 'quality', 'config']
        })
      );
    } catch (error) {
      Debug.warn('NodeRegistrations', '基础渲染节点导入失败:', error);
    }

    // 注册其他渲染节点
    if (renderingNodes.length > 0) {
      registerNodes(renderingNodes);
      Debug.log('NodeRegistrations', `其他渲染节点注册完成: ${renderingNodes.length}个节点`);
    }
  } catch (error) {
    Debug.error('NodeRegistrations', '渲染节点注册失败:', error);
  }
}

// 其他注册函数的占位符实现
async function registerPhysicsNodes(): Promise<void> {
  Debug.log('NodeRegistrations', '物理节点注册完成: 0个节点');
}

async function registerAnimationNodes(): Promise<void> {
  Debug.log('NodeRegistrations', '动画节点注册完成: 0个节点');
}

async function registerAudioNodes(): Promise<void> {
  Debug.log('NodeRegistrations', '音频节点注册完成: 0个节点');
}

async function registerInputNodes(): Promise<void> {
  Debug.log('NodeRegistrations', '输入节点注册完成: 0个节点');
}

async function registerNetworkNodes(): Promise<void> {
  Debug.log('NodeRegistrations', '网络节点注册完成: 0个节点');
}

async function registerAINodes(): Promise<void> {
  try {
    // 注册批次4：AI核心系统节点（21个）
    const { aiCoreSystemNodesRegistry } = await import('./AICoreSystemNodesRegistry');
    await aiCoreSystemNodesRegistry.registerAllNodes();
    Debug.log('NodeRegistrations', 'AI核心系统节点注册完成: 21个节点');

    // 注册批次11：AI与计算机视觉节点（40个）
    const { unregisteredAINodesRegistry } = await import('./UnregisteredAINodesRegistry');
    await unregisteredAINodesRegistry.registerAllNodes();
    Debug.log('NodeRegistrations', 'AI与计算机视觉节点注册完成: 40个节点');
    Debug.log('NodeRegistrations', '  - 模型管理节点: 10个');
    Debug.log('NodeRegistrations', '  - AutoML节点: 5个');
    Debug.log('NodeRegistrations', '  - 模型优化节点: 5个');
    Debug.log('NodeRegistrations', '  - 计算机视觉节点: 8个');
    Debug.log('NodeRegistrations', '  - 深度学习节点: 12个');

    Debug.log('NodeRegistrations', 'AI节点总计: 61个节点');
  } catch (error) {
    Debug.error('NodeRegistrations', 'AI节点注册失败:', error);
  }
}

async function registerSceneNodes(): Promise<void> {
  try {
    // 注册批次2：场景与资源管理节点（55个）
    const { sceneResourceNodesRegistry } = await import('./SceneResourceNodesRegistry');
    await sceneResourceNodesRegistry.registerAllNodes();
    Debug.log('NodeRegistrations', '场景与资源管理节点注册完成: 55个节点');
  } catch (error) {
    Debug.error('NodeRegistrations', '场景节点注册失败:', error);
  }
}

async function registerResourceNodes(): Promise<void> {
  // 资源节点已在registerSceneNodes中注册
  Debug.log('NodeRegistrations', '资源节点已在场景节点注册中完成');
}

async function registerIndustrialNodes(): Promise<void> {
  try {
    Debug.log('NodeRegistrations', '开始注册批次5：工业制造系统节点...');

    // 导入并调用工业制造系统节点注册表
    const { IndustrialManufacturingNodesRegistry } = await import('./IndustrialManufacturingNodesRegistry');
    const registry = IndustrialManufacturingNodesRegistry.getInstance();
    registry.registerAllNodes();

    Debug.log('NodeRegistrations', '批次5：工业制造系统节点注册完成: 60个节点');
    Debug.log('NodeRegistrations', '  - MES系统节点: 15个');
    Debug.log('NodeRegistrations', '  - 设备管理节点: 10个');
    Debug.log('NodeRegistrations', '  - 预测性维护节点: 10个');
    Debug.log('NodeRegistrations', '  - 质量管理节点: 10个');
    Debug.log('NodeRegistrations', '  - 供应链管理节点: 8个');
    Debug.log('NodeRegistrations', '  - 能源管理节点: 7个');
  } catch (error) {
    Debug.error('NodeRegistrations', '批次5：工业制造系统节点注册失败:', error);
  }
}

async function registerEdgeComputingNodes(): Promise<void> {
  Debug.log('NodeRegistrations', '边缘计算节点注册完成: 0个节点');
}

async function registerSpecializedNodes(): Promise<void> {
  Debug.log('NodeRegistrations', '专业应用节点注册完成: 0个节点');
}

async function registerDataServiceNodes(): Promise<void> {
  Debug.log('NodeRegistrations', '数据服务节点注册完成: 0个节点');
}

async function registerUINodes(): Promise<void> {
  Debug.log('NodeRegistrations', 'UI节点注册完成: 0个节点');
}

async function registerMotionCaptureNodes(): Promise<void> {
  Debug.log('NodeRegistrations', '动作捕捉节点注册完成: 0个节点');
}

/**
 * 注册批次3：高级渲染效果节点
 */
async function registerAdvancedRenderingEffectsNodes(): Promise<void> {
  try {
    Debug.log('NodeRegistrations', '开始注册批次3：高级渲染效果节点...');

    // 导入并调用高级渲染效果注册表
    const { registerAdvancedRenderingEffectsNodes } = await import('./AdvancedRenderingEffectsRegistry');
    registerAdvancedRenderingEffectsNodes();

    Debug.log('NodeRegistrations', '批次3：高级渲染效果节点注册完成: 59个节点');
    Debug.log('NodeRegistrations', '  - 高级着色器节点: 6个');
    Debug.log('NodeRegistrations', '  - 高级后处理节点: 20个');
    Debug.log('NodeRegistrations', '  - 场景过渡节点: 1个');
    Debug.log('NodeRegistrations', '  - 场景生成节点: 2个');
    Debug.log('NodeRegistrations', '  - 工业自动化节点: 5个');
    Debug.log('NodeRegistrations', '  - 服务器端节点: 25个');
  } catch (error) {
    Debug.error('NodeRegistrations', '批次3：高级渲染效果节点注册失败:', error);
  }
}

/**
 * 注册批次6：服务器与云端节点
 */
async function registerServerCloudNodes(): Promise<void> {
  try {
    Debug.log('NodeRegistrations', '开始注册批次6：服务器与云端节点...');

    // 导入并调用服务器与云端节点注册表
    const { ServerCloudNodesRegistry } = await import('./ServerCloudNodesRegistry');
    const registry = ServerCloudNodesRegistry.getInstance();
    const success = registry.registerAll();

    if (success) {
      Debug.log('NodeRegistrations', '批次6：服务器与云端节点注册完成: 58个节点');
      Debug.log('NodeRegistrations', '  - 文件服务节点: 10个');
      Debug.log('NodeRegistrations', '  - 认证授权节点: 7个');
      Debug.log('NodeRegistrations', '  - 通知服务节点: 8个');
      Debug.log('NodeRegistrations', '  - 监控服务节点: 5个');
      Debug.log('NodeRegistrations', '  - 项目管理节点: 10个');
      Debug.log('NodeRegistrations', '  - 边缘设备节点: 18个');
    } else {
      throw new Error('服务器与云端节点注册失败');
    }
  } catch (error) {
    Debug.error('NodeRegistrations', '批次6：服务器与云端节点注册失败:', error);
  }
}

/**
 * 注册批次7：边缘计算与5G节点
 */
async function registerEdgeComputing5GNodes(): Promise<void> {
  try {
    Debug.log('NodeRegistrations', '开始注册批次7：边缘计算与5G节点...');

    // 导入并调用边缘计算与5G节点注册表
    const { EdgeComputing5GNodesRegistry } = await import('./EdgeComputing5GNodesRegistry');
    const registry = new EdgeComputing5GNodesRegistry();
    registry.registerAllNodes();

    if (registry.isRegistered()) {
      Debug.log('NodeRegistrations', '批次7：边缘计算与5G节点注册完成: 59个节点');
      Debug.log('NodeRegistrations', '  - 边缘设备管理节点: 7个');
      Debug.log('NodeRegistrations', '  - 边缘AI节点: 12个');
      Debug.log('NodeRegistrations', '  - 云边协调节点: 8个');
      Debug.log('NodeRegistrations', '  - 边缘路由节点: 6个');
      Debug.log('NodeRegistrations', '  - 5G网络节点: 8个');
      Debug.log('NodeRegistrations', '  - 计算机视觉节点: 15个');
      Debug.log('NodeRegistrations', '  - 自然语言处理节点: 3个');
    } else {
      throw new Error('边缘计算与5G节点注册失败');
    }
  } catch (error) {
    Debug.error('NodeRegistrations', '批次7：边缘计算与5G节点注册失败:', error);
  }
}

/**
 * 注册批次8：交互体验系统节点
 */
async function registerInteractionExperienceNodes(): Promise<void> {
  try {
    Debug.log('NodeRegistrations', '开始注册批次8：交互体验系统节点...');

    // 导入并调用交互体验系统节点注册表
    const { InteractionExperienceNodesRegistry } = await import('./InteractionExperienceNodesRegistry');
    const registry = new InteractionExperienceNodesRegistry();
    registry.registerAllNodes();

    if (registry.isRegistered()) {
      Debug.log('NodeRegistrations', '批次8：交互体验系统节点注册完成: 58个节点');
      const stats = registry.getNodeCategoryStats();
      for (const [category, count] of Object.entries(stats)) {
        Debug.log('NodeRegistrations', `  - ${category}节点: ${count}个`);
      }
    } else {
      throw new Error('交互体验系统节点注册失败');
    }
  } catch (error) {
    Debug.error('NodeRegistrations', '批次8：交互体验系统节点注册失败:', error);
  }
}

/**
 * 注册批次9：专业应用领域节点
 */
async function registerProfessionalApplicationNodes(): Promise<void> {
  try {
    Debug.log('NodeRegistrations', '开始注册批次9：专业应用领域节点...');

    const { professionalApplicationNodesRegistry } = await import('./ProfessionalApplicationNodesRegistry');
    await professionalApplicationNodesRegistry.registerAllNodes();

    if (professionalApplicationNodesRegistry.isRegistered()) {
      const stats = professionalApplicationNodesRegistry.getRegistrationStats();
      Debug.log('NodeRegistrations', `批次9：专业应用领域节点注册完成: ${stats.totalNodes}个节点`);

      const categories = professionalApplicationNodesRegistry.getNodeCategories();
      for (const [category, count] of Object.entries(categories)) {
        Debug.log('NodeRegistrations', `  - ${category}节点: ${count}个`);
      }

      Debug.log('NodeRegistrations', `注册耗时: ${stats.registrationTime.toFixed(2)}ms`);

      if (stats.errors.length > 0) {
        Debug.warn('NodeRegistrations', `注册过程中发生 ${stats.errors.length} 个错误:`);
        stats.errors.forEach((error, index) => {
          Debug.warn('NodeRegistrations', `  ${index + 1}. ${error}`);
        });
      }
    } else {
      throw new Error('专业应用领域节点注册失败');
    }
  } catch (error) {
    Debug.error('NodeRegistrations', '批次9：专业应用领域节点注册失败:', error);
  }
}

/**
 * 注册批次10：内容创作与输入节点
 */
async function registerContentCreationInputNodes(): Promise<void> {
  try {
    Debug.log('NodeRegistrations', '开始注册批次10：内容创作与输入节点...');

    // 导入内容创作与输入节点注册表
    const { registerContentCreationInputNodes } = await import('./ContentCreationInputNodesRegistry');

    // 获取节点注册表实例
    const nodeRegistry = NodeRegistry.getInstance();

    // 注册所有内容创作与输入节点
    registerContentCreationInputNodes(nodeRegistry);

    Debug.log('NodeRegistrations', '批次10：内容创作与输入节点注册完成: 53个节点');
    Debug.log('NodeRegistrations', '  - 地形编辑节点: 12个');
    Debug.log('NodeRegistrations', '  - 动画编辑节点: 17个');
    Debug.log('NodeRegistrations', '  - 水体系统节点: 2个');
    Debug.log('NodeRegistrations', '  - VR/AR输入节点: 8个');
    Debug.log('NodeRegistrations', '  - 手势识别节点: 4个');
    Debug.log('NodeRegistrations', '  - 组件系统节点: 6个');
    Debug.log('NodeRegistrations', '  - 变换系统节点: 4个');

  } catch (error) {
    Debug.error('NodeRegistrations', '批次10：内容创作与输入节点注册失败:', error);
    throw error;
  }
}

/**
 * 注册批次12：物理与动画系统节点
 */
async function registerPhysicsAnimationNodes(): Promise<void> {
  Debug.log('NodeRegistrations', '开始注册批次12：物理与动画系统节点...');

  try {
    const { PhysicsAnimationNodesRegistry } = await import('./PhysicsAnimationNodesRegistry');
    const nodeRegistry = NodeRegistry.getInstance();
    const registry = new PhysicsAnimationNodesRegistry(nodeRegistry);

    // 注册所有物理与动画系统节点
    registry.registerAll();

    if (registry.isRegistered()) {
      Debug.log('NodeRegistrations', '批次12：物理与动画系统节点注册完成: 28个节点');
      Debug.log('NodeRegistrations', '  - 物理系统节点: 17个');
      Debug.log('NodeRegistrations', '    ├─ 软体物理: 4个 (软体物理、流体模拟、布料模拟、布料系统)');
      Debug.log('NodeRegistrations', '    ├─ 高级物理: 3个 (绳索模拟、破坏效果、物理约束)');
      Debug.log('NodeRegistrations', '    └─ 性能优化: 4个 (物理优化、物理LOD、性能监控、批处理)');
      Debug.log('NodeRegistrations', '  - 动画系统节点: 11个');
      Debug.log('NodeRegistrations', '    ├─ 基础动画: 4个 (补间动画、关键帧动画、混合树、状态机)');
      Debug.log('NodeRegistrations', '    ├─ 高级动画: 4个 (IK系统、动画混合、动画事件、动画重定向)');
      Debug.log('NodeRegistrations', '    └─ 动画工具: 3个 (动画烘焙、动画导出、动画导入)');

      // 验证注册
      const isValid = registry.validateRegistration();
      if (isValid) {
        Debug.log('NodeRegistrations', '✅ 批次12节点注册验证通过');
      } else {
        Debug.warn('NodeRegistrations', '⚠️ 批次12节点注册验证存在问题');
      }
    } else {
      throw new Error('物理与动画系统节点注册失败');
    }
  } catch (error) {
    Debug.error('NodeRegistrations', '批次12：物理与动画系统节点注册失败:', error);
    throw error;
  }
}

/**
 * 注册批次1.5、2.1、2.2：模型管理、VR/AR输入、高级输入节点
 */
async function registerBatch15And21And22Nodes(): Promise<void> {
  try {
    Debug.log('NodeRegistrations', '开始注册批次1.5、2.1、2.2：模型管理、VR/AR输入、高级输入节点...');

    // 导入并调用批次1.5、2.1、2.2节点注册表
    const { Batch15And21And22NodesRegistry } = await import('./Batch15And21And22NodesRegistry');
    const nodeRegistry = NodeRegistry.getInstance();
    const registry = new Batch15And21And22NodesRegistry(nodeRegistry);

    // 注册所有节点
    registry.registerAllNodes();

    if (registry.isRegistered()) {
      Debug.log('NodeRegistrations', '批次1.5、2.1、2.2节点注册完成: 37个节点');
      Debug.log('NodeRegistrations', '  - 批次1.5 模型管理节点: 25个');
      Debug.log('NodeRegistrations', '    ├─ 模型注册表: 1个 (模型注册表)');
      Debug.log('NodeRegistrations', '    ├─ 模型验证测试: 3个 (模型验证、模型测试、模型基准测试)');
      Debug.log('NodeRegistrations', '    ├─ 模型比较分析: 3个 (模型比较、模型指标、模型审计)');
      Debug.log('NodeRegistrations', '    ├─ 模型治理生命周期: 2个 (模型治理、模型生命周期)');
      Debug.log('NodeRegistrations', '    ├─ 模型部署管理: 4个 (模型回滚、模型A/B测试、模型金丝雀发布、模型影子测试)');
      Debug.log('NodeRegistrations', '    ├─ 模型反馈重训练: 2个 (模型反馈、模型重训练)');
      Debug.log('NodeRegistrations', '    ├─ 模型监控性能: 3个 (模型漂移检测、模型性能监控、模型资源管理)');
      Debug.log('NodeRegistrations', '    ├─ 模型安全隐私: 2个 (模型安全、模型隐私保护)');
      Debug.log('NodeRegistrations', '    └─ 模型公平性解释: 5个 (模型公平性、模型可解释性、模型文档)');
      Debug.log('NodeRegistrations', '  - 批次2.1 VR/AR输入节点: 8个');
      Debug.log('NodeRegistrations', '    ├─ VR输入: 2个 (VR控制器输入、空间输入)');
      Debug.log('NodeRegistrations', '    ├─ AR输入: 1个 (AR触摸输入)');
      Debug.log('NodeRegistrations', '    ├─ 追踪输入: 3个 (眼动追踪输入、手部追踪输入、语音命令输入)');
      Debug.log('NodeRegistrations', '    └─ 其他输入: 2个 (触觉反馈输入、运动控制器)');
      Debug.log('NodeRegistrations', '  - 批次2.2 高级输入节点: 4个');
      Debug.log('NodeRegistrations', '    ├─ 触控输入: 2个 (多点触控手势、压感输入)');
      Debug.log('NodeRegistrations', '    └─ 传感器输入: 2个 (倾斜输入、接近感应输入)');

      // 验证注册
      const registeredTypes = registry.getAllRegisteredNodeTypes();
      if (registeredTypes.length === 37) {
        Debug.log('NodeRegistrations', '✅ 批次1.5、2.1、2.2节点注册验证通过');
      } else {
        Debug.warn('NodeRegistrations', `⚠️ 批次1.5、2.1、2.2节点注册数量不匹配: 期望37个，实际${registeredTypes.length}个`);
      }
    } else {
      throw new Error('批次1.5、2.1、2.2节点注册失败');
    }
  } catch (error) {
    Debug.error('NodeRegistrations', '批次1.5、2.1、2.2节点注册失败:', error);
    throw error;
  }
}
