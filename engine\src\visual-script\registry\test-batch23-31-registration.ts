/**
 * 测试批次2.3-3.1节点注册
 * 验证41个节点是否正确注册到编辑器
 */

import { Debug } from '../../utils/Debug';
import Batch23To31NodesRegistry from './Batch23To31NodesRegistry';

/**
 * 测试节点注册
 */
export async function testBatch23To31NodesRegistration(): Promise<void> {
  try {
    Debug.log('TestBatch23To31', '开始测试批次2.3-3.1节点注册...');

    // 获取注册表实例
    const registry = Batch23To31NodesRegistry;

    // 检查注册表是否已初始化
    if (registry.isRegistered()) {
      Debug.log('TestBatch23To31', '节点已经注册过了');
    } else {
      Debug.log('TestBatch23To31', '开始注册节点...');
      
      // 注册所有节点
      registry.registerAllNodes();
      
      // 等待一段时间确保注册完成
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // 验证注册结果
    const registeredCount = registry.getRegisteredNodeCount();
    const expectedCount = 41; // 6+2+4+6+4+19 = 41个节点

    Debug.log('TestBatch23To31', `已注册节点数量: ${registeredCount}/${expectedCount}`);

    if (registeredCount === expectedCount) {
      Debug.log('TestBatch23To31', '✅ 所有节点注册成功！');
    } else {
      Debug.warn('TestBatch23To31', `⚠️ 节点注册数量不匹配，期望${expectedCount}个，实际${registeredCount}个`);
    }

    // 获取已注册的节点类型列表
    const registeredTypes = registry.getRegisteredNodeTypes();
    Debug.log('TestBatch23To31', '已注册的节点类型:');
    registeredTypes.forEach((type, index) => {
      Debug.log('TestBatch23To31', `  ${index + 1}. ${type}`);
    });

    // 验证特定节点类型
    const expectedNodeTypes = [
      // 传感器输入节点 (6个)
      'input/accelerometer',
      'input/gyroscope', 
      'input/compass',
      'input/pressure_sensor',
      'input/light_sensor',
      'input/proximity',
      
      // 语音输入节点 (2个)
      'SpeechRecognitionNode',
      'VoiceActivityDetectionNode',
      
      // 手势识别节点 (4个)
      'HandGestureRecognitionNode',
      'FingerTrackingNode',
      'PalmDetectionNode',
      'GestureClassificationNode',
      
      // 支付系统节点 (6个)
      'PaymentGatewayNode',
      'SubscriptionNode',
      'WalletSystemNode',
      'TransactionNode',
      'RefundNode',
      'PaymentAnalyticsNode',
      
      // 第三方集成节点 (4个)
      'SocialMediaIntegrationNode',
      'CloudStorageIntegrationNode',
      'AnalyticsIntegrationNode',
      'CRMIntegrationNode',
      
      // 空间信息节点 (19个)
      'GISDataLoader',
      'CoordinateTransform',
      'SpatialQuery',
      'Geofencing',
      'RouteCalculation',
      'LocationServices',
      'MapRendering',
      'SpatialAnalysis',
      'GeospatialVisualization',
      'TerrainAnalysis',
      'WeatherData',
      'SatelliteImagery',
      'GPSTracking',
      'Navigation',
      'LandmarkDetection',
      'UrbanPlanning',
      'EnvironmentalMonitoring',
      'DisasterManagement',
      'SmartCity'
    ];

    // 检查每个期望的节点类型是否已注册
    let missingNodes: string[] = [];
    let foundNodes: string[] = [];

    expectedNodeTypes.forEach(expectedType => {
      if (registeredTypes.includes(expectedType)) {
        foundNodes.push(expectedType);
      } else {
        missingNodes.push(expectedType);
      }
    });

    Debug.log('TestBatch23To31', `✅ 找到的节点: ${foundNodes.length}个`);
    if (missingNodes.length > 0) {
      Debug.warn('TestBatch23To31', `❌ 缺失的节点: ${missingNodes.length}个`);
      missingNodes.forEach(type => {
        Debug.warn('TestBatch23To31', `  - ${type}`);
      });
    }

    // 生成测试报告
    const testReport = {
      totalExpected: expectedCount,
      totalRegistered: registeredCount,
      foundNodes: foundNodes.length,
      missingNodes: missingNodes.length,
      success: registeredCount >= expectedCount * 0.8, // 80%以上算成功
      registeredTypes,
      missingTypes: missingNodes
    };

    Debug.log('TestBatch23To31', '测试报告:', testReport);

    if (testReport.success) {
      Debug.log('TestBatch23To31', '🎉 批次2.3-3.1节点注册测试通过！');
    } else {
      Debug.error('TestBatch23To31', '❌ 批次2.3-3.1节点注册测试失败！');
    }

    return testReport;

  } catch (error) {
    Debug.error('TestBatch23To31', '测试过程中发生错误:', error);
    throw error;
  }
}

/**
 * 运行测试
 */
export async function runTest(): Promise<void> {
  try {
    await testBatch23To31NodesRegistration();
  } catch (error) {
    Debug.error('TestBatch23To31', '测试运行失败:', error);
  }
}

// 如果直接运行此文件，则执行测试
if (typeof window !== 'undefined' && (window as any).runBatch23To31Test) {
  runTest();
}
