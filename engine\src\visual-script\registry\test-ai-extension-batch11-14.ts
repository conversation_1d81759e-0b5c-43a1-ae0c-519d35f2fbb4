/**
 * 测试批次1.1-1.4 AI扩展节点注册表
 * 验证36个节点的注册和功能
 */

import { aiExtensionNodesRegistry, AI_EXTENSION_NODE_TYPES } from './AIExtensionNodesRegistry';
import { NodeRegistry } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

/**
 * 测试AI扩展节点注册表
 */
export async function testAIExtensionBatch11To14(): Promise<void> {
  Debug.log('TestAIExtensionBatch11To14', '开始测试批次1.1-1.4 AI扩展节点注册表...');

  try {
    // 1. 注册所有节点
    aiExtensionNodesRegistry.registerAllNodes();
    Debug.log('TestAIExtensionBatch11To14', '✅ 节点注册完成');

    // 2. 验证注册状态
    const isRegistered = aiExtensionNodesRegistry.isRegistered();
    if (!isRegistered) {
      throw new Error('注册表状态异常');
    }
    Debug.log('TestAIExtensionBatch11To14', '✅ 注册状态验证通过');

    // 3. 验证节点数量
    const registeredTypes = aiExtensionNodesRegistry.getAllRegisteredNodeTypes();
    const expectedCount = 36; // 11 + 8 + 10 + 7
    
    if (registeredTypes.length !== expectedCount) {
      throw new Error(`节点数量不匹配，期望${expectedCount}个，实际${registeredTypes.length}个`);
    }
    Debug.log('TestAIExtensionBatch11To14', `✅ 节点数量验证通过：${registeredTypes.length}个`);

    // 4. 验证深度学习扩展节点（11个）
    await testDeepLearningExtensionNodes();

    // 5. 验证机器学习扩展节点（8个）
    await testMachineLearningExtensionNodes();

    // 6. 验证AI工具节点（10个）
    await testAIToolNodes();

    // 7. 验证自然语言处理节点（7个）
    await testNaturalLanguageProcessingNodes();

    // 8. 验证节点创建和执行
    await testNodeCreationAndExecution();

    Debug.log('TestAIExtensionBatch11To14', '🎉 批次1.1-1.4 AI扩展节点注册表测试全部通过！');

  } catch (error) {
    Debug.error('TestAIExtensionBatch11To14', '测试失败:', error);
    throw error;
  }
}

/**
 * 测试深度学习扩展节点
 */
async function testDeepLearningExtensionNodes(): Promise<void> {
  Debug.log('TestAIExtensionBatch11To14', '测试深度学习扩展节点...');

  const deepLearningNodes = [
    AI_EXTENSION_NODE_TYPES.TRANSFORMER_MODEL,
    AI_EXTENSION_NODE_TYPES.GAN_MODEL,
    AI_EXTENSION_NODE_TYPES.VAE_MODEL,
    AI_EXTENSION_NODE_TYPES.ATTENTION_MECHANISM,
    AI_EXTENSION_NODE_TYPES.EMBEDDING_LAYER,
    AI_EXTENSION_NODE_TYPES.DROPOUT_LAYER,
    AI_EXTENSION_NODE_TYPES.BATCH_NORMALIZATION,
    AI_EXTENSION_NODE_TYPES.ACTIVATION_FUNCTION,
    AI_EXTENSION_NODE_TYPES.LOSS_FUNCTION,
    AI_EXTENSION_NODE_TYPES.OPTIMIZER,
    AI_EXTENSION_NODE_TYPES.LEARNING_RATE_SCHEDULER
  ];

  for (const nodeType of deepLearningNodes) {
    const nodeInfo = NodeRegistry.getNode(nodeType);
    if (!nodeInfo) {
      throw new Error(`深度学习节点未注册: ${nodeType}`);
    }
  }

  Debug.log('TestAIExtensionBatch11To14', '✅ 深度学习扩展节点验证通过：11个');
}

/**
 * 测试机器学习扩展节点
 */
async function testMachineLearningExtensionNodes(): Promise<void> {
  Debug.log('TestAIExtensionBatch11To14', '测试机器学习扩展节点...');

  const machineLearningNodes = [
    AI_EXTENSION_NODE_TYPES.RANDOM_FOREST,
    AI_EXTENSION_NODE_TYPES.SUPPORT_VECTOR_MACHINE,
    AI_EXTENSION_NODE_TYPES.KMEANS_CLUSTERING,
    AI_EXTENSION_NODE_TYPES.PCA,
    AI_EXTENSION_NODE_TYPES.LINEAR_REGRESSION,
    AI_EXTENSION_NODE_TYPES.LOGISTIC_REGRESSION,
    AI_EXTENSION_NODE_TYPES.DECISION_TREE,
    AI_EXTENSION_NODE_TYPES.ENSEMBLE_METHOD
  ];

  for (const nodeType of machineLearningNodes) {
    const nodeInfo = NodeRegistry.getNode(nodeType);
    if (!nodeInfo) {
      throw new Error(`机器学习节点未注册: ${nodeType}`);
    }
  }

  Debug.log('TestAIExtensionBatch11To14', '✅ 机器学习扩展节点验证通过：8个');
}

/**
 * 测试AI工具节点
 */
async function testAIToolNodes(): Promise<void> {
  Debug.log('TestAIExtensionBatch11To14', '测试AI工具节点...');

  const aiToolNodes = [
    AI_EXTENSION_NODE_TYPES.MODEL_DEPLOYMENT,
    AI_EXTENSION_NODE_TYPES.MODEL_MONITORING,
    AI_EXTENSION_NODE_TYPES.MODEL_VERSIONING,
    AI_EXTENSION_NODE_TYPES.AUTO_ML,
    AI_EXTENSION_NODE_TYPES.EXPLAINABLE_AI,
    AI_EXTENSION_NODE_TYPES.AI_ETHICS,
    AI_EXTENSION_NODE_TYPES.MODEL_COMPRESSION,
    AI_EXTENSION_NODE_TYPES.QUANTIZATION,
    AI_EXTENSION_NODE_TYPES.PRUNING,
    AI_EXTENSION_NODE_TYPES.DISTILLATION
  ];

  for (const nodeType of aiToolNodes) {
    const nodeInfo = NodeRegistry.getNode(nodeType);
    if (!nodeInfo) {
      throw new Error(`AI工具节点未注册: ${nodeType}`);
    }
  }

  Debug.log('TestAIExtensionBatch11To14', '✅ AI工具节点验证通过：10个');
}

/**
 * 测试自然语言处理节点
 */
async function testNaturalLanguageProcessingNodes(): Promise<void> {
  Debug.log('TestAIExtensionBatch11To14', '测试自然语言处理节点...');

  const nlpNodes = [
    AI_EXTENSION_NODE_TYPES.TEXT_CLASSIFICATION,
    AI_EXTENSION_NODE_TYPES.NAMED_ENTITY_RECOGNITION,
    AI_EXTENSION_NODE_TYPES.SENTIMENT_ANALYSIS,
    AI_EXTENSION_NODE_TYPES.TEXT_SUMMARIZATION,
    AI_EXTENSION_NODE_TYPES.MACHINE_TRANSLATION,
    AI_EXTENSION_NODE_TYPES.QUESTION_ANSWERING,
    AI_EXTENSION_NODE_TYPES.TEXT_GENERATION
  ];

  for (const nodeType of nlpNodes) {
    const nodeInfo = NodeRegistry.getNode(nodeType);
    if (!nodeInfo) {
      throw new Error(`自然语言处理节点未注册: ${nodeType}`);
    }
  }

  Debug.log('TestAIExtensionBatch11To14', '✅ 自然语言处理节点验证通过：7个');
}

/**
 * 测试节点创建和执行
 */
async function testNodeCreationAndExecution(): Promise<void> {
  Debug.log('TestAIExtensionBatch11To14', '测试节点创建和执行...');

  // 测试学习率调度器节点
  const schedulerNode = NodeRegistry.createNode(AI_EXTENSION_NODE_TYPES.LEARNING_RATE_SCHEDULER);
  if (!schedulerNode) {
    throw new Error('学习率调度器节点创建失败');
  }

  const schedulerResult = schedulerNode.execute({
    initialLearningRate: 0.001,
    schedulerType: 'exponential',
    currentEpoch: 100,
    gamma: 0.95
  });

  if (!schedulerResult.success) {
    throw new Error('学习率调度器节点执行失败');
  }

  // 测试随机森林节点
  const randomForestNode = NodeRegistry.createNode(AI_EXTENSION_NODE_TYPES.RANDOM_FOREST);
  if (!randomForestNode) {
    throw new Error('随机森林节点创建失败');
  }

  const forestResult = randomForestNode.execute({
    trainingData: [[1, 2], [3, 4], [5, 6]],
    labels: ['A', 'B', 'A'],
    numTrees: 10
  });

  if (!forestResult.success) {
    throw new Error('随机森林节点执行失败');
  }

  Debug.log('TestAIExtensionBatch11To14', '✅ 节点创建和执行验证通过');
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testAIExtensionBatch11To14()
    .then(() => {
      console.log('🎉 批次1.1-1.4 AI扩展节点注册表测试成功完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    });
}
