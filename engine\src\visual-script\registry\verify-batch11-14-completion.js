/**
 * 验证批次1.1-1.4节点注册完成情况
 * 简单的JavaScript验证脚本
 */

console.log('🔍 验证批次1.1-1.4 AI扩展节点注册完成情况...\n');

// 验证AIExtensionNodesRegistry.ts文件内容
const fs = require('fs');
const path = require('path');

try {
  const registryPath = path.join(__dirname, 'AIExtensionNodesRegistry.ts');
  const registryContent = fs.readFileSync(registryPath, 'utf8');

  console.log('📋 检查注册表文件内容...');

  // 检查深度学习扩展节点（11个）
  const deepLearningNodes = [
    'TransformerModelNode',
    'GANModelNode', 
    'VAEModelNode',
    'AttentionMechanismNode',
    'EmbeddingLayerNode',
    'DropoutLayerNode',
    'BatchNormalizationNode',
    'ActivationFunctionNode',
    'LossFunctionNode',
    'OptimizerNode',
    'LearningRateSchedulerNode'
  ];

  console.log('\n🧠 深度学习扩展节点（11个）:');
  let deepLearningCount = 0;
  deepLearningNodes.forEach(node => {
    if (registryContent.includes(node)) {
      console.log(`  ✅ ${node}`);
      deepLearningCount++;
    } else {
      console.log(`  ❌ ${node} - 未找到`);
    }
  });

  // 检查机器学习扩展节点（8个）
  const machineLearningNodes = [
    'RandomForestNode',
    'SupportVectorMachineNode',
    'KMeansClusteringNode',
    'PCANode',
    'LinearRegressionNode',
    'LogisticRegressionNode',
    'DecisionTreeNode',
    'EnsembleMethodNode'
  ];

  console.log('\n🤖 机器学习扩展节点（8个）:');
  let machineLearningCount = 0;
  machineLearningNodes.forEach(node => {
    if (registryContent.includes(node)) {
      console.log(`  ✅ ${node}`);
      machineLearningCount++;
    } else {
      console.log(`  ❌ ${node} - 未找到`);
    }
  });

  // 检查AI工具节点（10个）
  const aiToolNodes = [
    'ModelDeploymentNode',
    'ModelMonitoringNode',
    'ModelVersioningNode',
    'AutoMLNode',
    'ExplainableAINode',
    'AIEthicsNode',
    'ModelCompressionNode',
    'QuantizationNode',
    'PruningNode',
    'DistillationNode'
  ];

  console.log('\n🛠️ AI工具节点（10个）:');
  let aiToolCount = 0;
  aiToolNodes.forEach(node => {
    if (registryContent.includes(node)) {
      console.log(`  ✅ ${node}`);
      aiToolCount++;
    } else {
      console.log(`  ❌ ${node} - 未找到`);
    }
  });

  // 检查自然语言处理节点（7个）
  const nlpNodes = [
    'TextClassificationNode',
    'NamedEntityRecognitionNode',
    'SentimentAnalysisNode',
    'TextSummarizationNode',
    'MachineTranslationNode',
    'QuestionAnsweringNode',
    'TextGenerationNode'
  ];

  console.log('\n💬 自然语言处理节点（7个）:');
  let nlpCount = 0;
  nlpNodes.forEach(node => {
    if (registryContent.includes(node)) {
      console.log(`  ✅ ${node}`);
      nlpCount++;
    } else {
      console.log(`  ❌ ${node} - 未找到`);
    }
  });

  // 统计结果
  const totalExpected = 36;
  const totalFound = deepLearningCount + machineLearningCount + aiToolCount + nlpCount;

  console.log('\n📊 统计结果:');
  console.log(`  深度学习扩展节点: ${deepLearningCount}/11`);
  console.log(`  机器学习扩展节点: ${machineLearningCount}/8`);
  console.log(`  AI工具节点: ${aiToolCount}/10`);
  console.log(`  自然语言处理节点: ${nlpCount}/7`);
  console.log(`  总计: ${totalFound}/${totalExpected}`);

  // 检查节点类型常量
  console.log('\n🔗 检查节点类型常量:');
  const hasLearningRateScheduler = registryContent.includes('LEARNING_RATE_SCHEDULER');
  console.log(`  LEARNING_RATE_SCHEDULER: ${hasLearningRateScheduler ? '✅' : '❌'}`);

  // 检查注册方法
  console.log('\n⚙️ 检查注册方法:');
  const hasRegisterMethod = registryContent.includes('registerDeepLearningExtensionNodes');
  console.log(`  registerDeepLearningExtensionNodes: ${hasRegisterMethod ? '✅' : '❌'}`);

  // 检查学习率调度器节点实现文件
  console.log('\n📁 检查节点实现文件:');
  const deepLearningNodes5Path = path.join(__dirname, '../nodes/ai/DeepLearningNodes5.ts');
  if (fs.existsSync(deepLearningNodes5Path)) {
    const deepLearningNodes5Content = fs.readFileSync(deepLearningNodes5Path, 'utf8');
    const hasLearningRateSchedulerImpl = deepLearningNodes5Content.includes('LearningRateSchedulerNode');
    console.log(`  LearningRateSchedulerNode实现: ${hasLearningRateSchedulerImpl ? '✅' : '❌'}`);
  } else {
    console.log(`  ❌ DeepLearningNodes5.ts文件不存在`);
  }

  // 最终结果
  console.log('\n🎯 批次1.1-1.4完成状态:');
  if (totalFound === totalExpected) {
    console.log('  🎉 批次1.1-1.4节点注册已完成！');
    console.log('  ✅ 所有36个节点已成功注册到AIExtensionNodesRegistry.ts');
  } else {
    console.log(`  ⚠️ 批次1.1-1.4节点注册未完成，还缺少${totalExpected - totalFound}个节点`);
  }

} catch (error) {
  console.error('❌ 验证过程中出现错误:', error.message);
}
