/**
 * 注册批次2.3-3.1：交互体验与空间信息节点注册表
 * 注册41个节点到编辑器：
 * - 传感器输入节点（6个）
 * - 语音输入节点（2个）
 * - 手势识别节点（4个）
 * - 支付系统节点（6个）
 * - 第三方集成节点（4个）
 * - 空间信息节点（19个）
 */

import { NodeRegistry, NodeCategory, createNodeInfo } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

// 导入传感器输入节点（6个）
import {
  AccelerometerNode,
  GyroscopeNode,
  CompassNode as MagnetometerNode,
  PressureSensorNode as BarometerNode,
  LightSensorNode as AmbientLightSensorNode,
  ProximityNode as ProximitySensorNode
} from '../nodes/input/SensorInputNodes';

// 导入语音输入节点（2个）
import {
  SpeechRecognitionNode,
  VoiceActivityDetectionNode
} from '../nodes/input/VoiceInputNodes';

// 导入手势识别节点（4个）
import {
  HandGestureRecognitionNode,
  FingerTrackingNode,
  PalmDetectionNode,
  GestureClassificationNode
} from '../nodes/input/GestureRecognitionNodes';

// 导入支付系统节点（6个）
import {
  PaymentGatewayNode,
  SubscriptionNode,
  WalletSystemNode,
  TransactionNode,
  RefundNode,
  PaymentAnalyticsNode
} from '../nodes/payment/PaymentSystemNodes';

// 导入第三方集成节点（4个）
import {
  SocialMediaIntegrationNode,
  CloudStorageIntegrationNode,
  AnalyticsIntegrationNode,
  CRMIntegrationNode
} from '../nodes/integration/ThirdPartyIntegrationNodes';

// 导入空间信息节点（19个）
import {
  GISDataLoaderNode,
  CoordinateTransformNode,
  SpatialQueryNode as SpatialQueryNode,
  GeofencingNode,
  RouteCalculationNode,
  LocationServicesNode,
  MapRenderingNode,
  SpatialAnalysisNode,
  GeospatialVisualizationNode,
  TerrainAnalysisNode,
  WeatherDataNode,
  SatelliteImageryNode,
  GPSTrackingNode,
  NavigationNode,
  LandmarkDetectionNode,
  UrbanPlanningNode,
  EnvironmentalMonitoringNode,
  DisasterManagementNode,
  SmartCityNode
} from '../nodes/spatial/SpatialInformationNodes';

/**
 * 批次2.3-3.1节点注册表类
 */
export class Batch23To31NodesRegistry {
  private static instance: Batch23To31NodesRegistry;
  private registered: boolean = false;
  private registeredNodes: Set<string> = new Set();

  private constructor() {}

  public static getInstance(): Batch23To31NodesRegistry {
    if (!Batch23To31NodesRegistry.instance) {
      Batch23To31NodesRegistry.instance = new Batch23To31NodesRegistry();
    }
    return Batch23To31NodesRegistry.instance;
  }

  /**
   * 注册所有节点
   */
  public registerAllNodes(): void {
    if (this.registered) {
      Debug.warn('Batch23To31NodesRegistry', '节点已经注册过了');
      return;
    }

    try {
      Debug.log('Batch23To31NodesRegistry', '开始注册批次2.3-3.1节点...');

      // 注册传感器输入节点（6个）
      this.registerSensorInputNodes();

      // 注册语音输入节点（2个）
      this.registerVoiceInputNodes();

      // 注册手势识别节点（4个）
      this.registerGestureRecognitionNodes();

      // 注册支付系统节点（6个）
      this.registerPaymentSystemNodes();

      // 注册第三方集成节点（4个）
      this.registerThirdPartyIntegrationNodes();

      // 注册空间信息节点（19个）
      this.registerSpatialInformationNodes();

      this.registered = true;
      Debug.log('Batch23To31NodesRegistry', `成功注册${this.registeredNodes.size}个节点`);

    } catch (error) {
      Debug.error('Batch23To31NodesRegistry', '节点注册失败', error);
      throw error;
    }
  }

  /**
   * 注册传感器输入节点（6个）
   */
  private registerSensorInputNodes(): void {
    Debug.log('Batch23To31NodesRegistry', '注册传感器输入节点...');

    const sensorNodes = [
      { NodeClass: AccelerometerNode, icon: 'accelerometer', description: '检测设备加速度和运动状态' },
      { NodeClass: GyroscopeNode, icon: 'gyroscope', description: '检测设备旋转角度和方向' },
      { NodeClass: MagnetometerNode, icon: 'compass', description: '检测磁场方向和强度' },
      { NodeClass: BarometerNode, icon: 'barometer', description: '检测大气压力变化' },
      { NodeClass: AmbientLightSensorNode, icon: 'light-sensor', description: '检测环境光照强度' },
      { NodeClass: ProximitySensorNode, icon: 'proximity', description: '检测物体接近距离' }
    ];

    sensorNodes.forEach(({ NodeClass, icon, description }) => {
      this.registerNode(NodeClass, NodeCategory.INPUT, icon, '#FF9800', description);
    });
  }

  /**
   * 注册语音输入节点（2个）
   */
  private registerVoiceInputNodes(): void {
    Debug.log('Batch23To31NodesRegistry', '注册语音输入节点...');

    const voiceNodes = [
      { NodeClass: SpeechRecognitionNode, icon: 'microphone', description: '将语音转换为文本' },
      { NodeClass: VoiceActivityDetectionNode, icon: 'voice-detection', description: '检测语音活动状态' }
    ];

    voiceNodes.forEach(({ NodeClass, icon, description }) => {
      this.registerNode(NodeClass, NodeCategory.INPUT, icon, '#4CAF50', description);
    });
  }

  /**
   * 注册手势识别节点（4个）
   */
  private registerGestureRecognitionNodes(): void {
    Debug.log('Batch23To31NodesRegistry', '注册手势识别节点...');

    const gestureNodes = [
      { NodeClass: HandGestureRecognitionNode, icon: 'hand-gesture', description: '识别手部手势动作' },
      { NodeClass: FingerTrackingNode, icon: 'finger-tracking', description: '追踪手指位置和动作' },
      { NodeClass: PalmDetectionNode, icon: 'palm-detection', description: '检测手掌位置和状态' },
      { NodeClass: GestureClassificationNode, icon: 'gesture-classify', description: '分类和识别手势类型' }
    ];

    gestureNodes.forEach(({ NodeClass, icon, description }) => {
      this.registerNode(NodeClass, NodeCategory.INPUT, icon, '#9C27B0', description);
    });
  }

  /**
   * 注册支付系统节点（6个）
   */
  private registerPaymentSystemNodes(): void {
    Debug.log('Batch23To31NodesRegistry', '注册支付系统节点...');

    const paymentNodes = [
      { NodeClass: PaymentGatewayNode, icon: 'payment-gateway', description: '处理支付网关集成' },
      { NodeClass: SubscriptionNode, icon: 'subscription', description: '管理订阅服务' },
      { NodeClass: WalletSystemNode, icon: 'wallet', description: '管理数字钱包系统' },
      { NodeClass: TransactionNode, icon: 'transaction', description: '处理交易流程' },
      { NodeClass: RefundNode, icon: 'refund', description: '处理退款操作' },
      { NodeClass: PaymentAnalyticsNode, icon: 'payment-analytics', description: '分析支付数据' }
    ];

    paymentNodes.forEach(({ NodeClass, icon, description }) => {
      this.registerNode(NodeClass, NodeCategory.BUSINESS_LOGIC, icon, '#2196F3', description);
    });
  }

  /**
   * 注册第三方集成节点（4个）
   */
  private registerThirdPartyIntegrationNodes(): void {
    Debug.log('Batch23To31NodesRegistry', '注册第三方集成节点...');

    const integrationNodes = [
      { NodeClass: SocialMediaIntegrationNode, icon: 'social-media', description: '集成社交媒体平台' },
      { NodeClass: CloudStorageIntegrationNode, icon: 'cloud-storage', description: '集成云存储服务' },
      { NodeClass: AnalyticsIntegrationNode, icon: 'analytics', description: '集成数据分析工具' },
      { NodeClass: CRMIntegrationNode, icon: 'crm', description: '集成客户关系管理系统' }
    ];

    integrationNodes.forEach(({ NodeClass, icon, description }) => {
      this.registerNode(NodeClass, NodeCategory.INTEGRATION, icon, '#607D8B', description);
    });
  }

  /**
   * 注册空间信息节点（19个）
   */
  private registerSpatialInformationNodes(): void {
    Debug.log('Batch23To31NodesRegistry', '注册空间信息节点...');

    const spatialNodes = [
      { NodeClass: GISDataLoaderNode, icon: 'gis-data', description: '加载GIS地理信息数据' },
      { NodeClass: CoordinateTransformNode, icon: 'coordinate-transform', description: '转换地理坐标系统' },
      { NodeClass: SpatialQueryNode, icon: 'spatial-query', description: '执行空间查询操作' },
      { NodeClass: GeofencingNode, icon: 'geofencing', description: '创建和管理地理围栏' },
      { NodeClass: RouteCalculationNode, icon: 'route-calc', description: '计算最优路径' },
      { NodeClass: LocationServicesNode, icon: 'location', description: '提供位置服务' },
      { NodeClass: MapRenderingNode, icon: 'map-render', description: '渲染地图显示' },
      { NodeClass: SpatialAnalysisNode, icon: 'spatial-analysis', description: '执行空间分析' },
      { NodeClass: GeospatialVisualizationNode, icon: 'geo-viz', description: '地理空间数据可视化' },
      { NodeClass: TerrainAnalysisNode, icon: 'terrain', description: '分析地形数据' },
      { NodeClass: WeatherDataNode, icon: 'weather', description: '获取天气数据' },
      { NodeClass: SatelliteImageryNode, icon: 'satellite', description: '处理卫星影像' },
      { NodeClass: GPSTrackingNode, icon: 'gps', description: 'GPS位置追踪' },
      { NodeClass: NavigationNode, icon: 'navigation', description: '导航路径规划' },
      { NodeClass: LandmarkDetectionNode, icon: 'landmark', description: '检测地标建筑' },
      { NodeClass: UrbanPlanningNode, icon: 'urban-planning', description: '城市规划工具' },
      { NodeClass: EnvironmentalMonitoringNode, icon: 'environment', description: '环境监测系统' },
      { NodeClass: DisasterManagementNode, icon: 'disaster', description: '灾害管理系统' },
      { NodeClass: SmartCityNode, icon: 'smart-city', description: '智慧城市管理' }
    ];

    spatialNodes.forEach(({ NodeClass, icon, description }) => {
      this.registerNode(NodeClass, NodeCategory.SPATIAL, icon, '#795548', description);
    });
  }

  /**
   * 注册单个节点的通用方法
   */
  private registerNode(
    NodeClass: any,
    category: NodeCategory,
    icon: string,
    color: string,
    description: string
  ): void {
    try {
      const nodeInfo = createNodeInfo(
        NodeClass.TYPE,
        NodeClass.NAME,
        description,
        category,
        NodeClass,
        {
          icon,
          color,
          tags: [category.toLowerCase()],
          experimental: false
        }
      );

      NodeRegistry.registerNode(nodeInfo);
      this.registeredNodes.add(NodeClass.TYPE);

      Debug.log('Batch23To31NodesRegistry', `已注册节点: ${NodeClass.NAME} (${NodeClass.TYPE})`);

    } catch (error) {
      Debug.error('Batch23To31NodesRegistry', `注册节点失败: ${NodeClass.NAME}`, error);
      throw error;
    }
  }

  /**
   * 检查是否已注册
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 获取已注册的节点数量
   */
  public getRegisteredNodeCount(): number {
    return this.registeredNodes.size;
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getRegisteredNodeTypes(): string[] {
    return Array.from(this.registeredNodes);
  }
}

// 导出节点类数组
export const SENSOR_INPUT_NODES = [
  AccelerometerNode,
  GyroscopeNode,
  MagnetometerNode,
  BarometerNode,
  AmbientLightSensorNode,
  ProximitySensorNode
];

export const VOICE_INPUT_NODES = [
  SpeechRecognitionNode,
  VoiceActivityDetectionNode
];

export const GESTURE_RECOGNITION_NODES = [
  HandGestureRecognitionNode,
  FingerTrackingNode,
  PalmDetectionNode,
  GestureClassificationNode
];

export const PAYMENT_SYSTEM_NODES = [
  PaymentGatewayNode,
  SubscriptionNode,
  WalletSystemNode,
  TransactionNode,
  RefundNode,
  PaymentAnalyticsNode
];

export const THIRD_PARTY_INTEGRATION_NODES = [
  SocialMediaIntegrationNode,
  CloudStorageIntegrationNode,
  AnalyticsIntegrationNode,
  CRMIntegrationNode
];

export const SPATIAL_INFORMATION_NODES = [
  GISDataLoaderNode,
  CoordinateTransformNode,
  SpatialQueryNode,
  GeofencingNode,
  RouteCalculationNode,
  LocationServicesNode,
  MapRenderingNode,
  SpatialAnalysisNode,
  GeospatialVisualizationNode,
  TerrainAnalysisNode,
  WeatherDataNode,
  SatelliteImageryNode,
  GPSTrackingNode,
  NavigationNode,
  LandmarkDetectionNode,
  UrbanPlanningNode,
  EnvironmentalMonitoringNode,
  DisasterManagementNode,
  SmartCityNode
];

// 导出所有节点
export const ALL_BATCH_23_TO_31_NODES = [
  ...SENSOR_INPUT_NODES,
  ...VOICE_INPUT_NODES,
  ...GESTURE_RECOGNITION_NODES,
  ...PAYMENT_SYSTEM_NODES,
  ...THIRD_PARTY_INTEGRATION_NODES,
  ...SPATIAL_INFORMATION_NODES
];

// 默认导出注册表实例
export default Batch23To31NodesRegistry.getInstance();
