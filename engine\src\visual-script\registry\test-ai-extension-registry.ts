/**
 * AI扩展节点注册表测试文件
 * 用于验证批次1：AI与计算机视觉系统节点注册是否正常工作
 */

import { AIExtensionNodesRegistry, AI_EXTENSION_NODE_TYPES } from './AIExtensionNodesRegistry';
import { NodeRegistry } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

/**
 * 测试AI扩展节点注册表
 */
export class AIExtensionRegistryTest {
  private registry: AIExtensionNodesRegistry;

  constructor() {
    this.registry = AIExtensionNodesRegistry.getInstance();
  }

  /**
   * 运行所有测试
   */
  public async runAllTests(): Promise<void> {
    Debug.log('AIExtensionRegistryTest', '开始测试AI扩展节点注册表...');

    try {
      await this.testRegistryInitialization();
      await this.testNodeRegistration();
      await this.testNodeTypes();
      await this.testNodeStats();
      await this.testNodeCreation();

      Debug.log('AIExtensionRegistryTest', '✅ 所有测试通过！');
    } catch (error) {
      Debug.error('AIExtensionRegistryTest', '❌ 测试失败:', error);
      throw error;
    }
  }

  /**
   * 测试注册表初始化
   */
  private async testRegistryInitialization(): Promise<void> {
    Debug.log('AIExtensionRegistryTest', '测试注册表初始化...');

    // 测试单例模式
    const registry1 = AIExtensionNodesRegistry.getInstance();
    const registry2 = AIExtensionNodesRegistry.getInstance();
    
    if (registry1 !== registry2) {
      throw new Error('注册表单例模式失败');
    }

    Debug.log('AIExtensionRegistryTest', '✅ 注册表初始化测试通过');
  }

  /**
   * 测试节点注册
   */
  private async testNodeRegistration(): Promise<void> {
    Debug.log('AIExtensionRegistryTest', '测试节点注册...');

    // 检查注册前状态
    const beforeRegistration = this.registry.isRegistered();
    
    // 执行注册
    this.registry.registerAllNodes();
    
    // 检查注册后状态
    const afterRegistration = this.registry.isRegistered();
    
    if (!afterRegistration) {
      throw new Error('节点注册失败');
    }

    Debug.log('AIExtensionRegistryTest', '✅ 节点注册测试通过');
  }

  /**
   * 测试节点类型
   */
  private async testNodeTypes(): Promise<void> {
    Debug.log('AIExtensionRegistryTest', '测试节点类型...');

    const registeredTypes = this.registry.getAllRegisteredNodeTypes();
    const expectedTypes = Object.values(AI_EXTENSION_NODE_TYPES);

    // 检查节点类型数量
    if (registeredTypes.length !== expectedTypes.length) {
      throw new Error(`节点类型数量不匹配: 期望${expectedTypes.length}个，实际${registeredTypes.length}个`);
    }

    // 检查每个节点类型是否存在
    for (const expectedType of expectedTypes) {
      if (!registeredTypes.includes(expectedType)) {
        throw new Error(`缺少节点类型: ${expectedType}`);
      }
    }

    Debug.log('AIExtensionRegistryTest', '✅ 节点类型测试通过');
  }

  /**
   * 测试节点统计信息
   */
  private async testNodeStats(): Promise<void> {
    Debug.log('AIExtensionRegistryTest', '测试节点统计信息...');

    const stats = this.registry.getNodeStats();
    const expectedStats = {
      deepLearningExtension: 11,
      machineLearningExtension: 8,
      aiTools: 10,
      naturalLanguageProcessing: 7,
      modelManagement: 5,
      total: 41
    };

    // 检查统计信息
    for (const [key, expectedValue] of Object.entries(expectedStats)) {
      if (stats[key] !== expectedValue) {
        throw new Error(`统计信息不匹配 ${key}: 期望${expectedValue}，实际${stats[key]}`);
      }
    }

    Debug.log('AIExtensionRegistryTest', '✅ 节点统计信息测试通过');
  }

  /**
   * 测试节点创建
   */
  private async testNodeCreation(): Promise<void> {
    Debug.log('AIExtensionRegistryTest', '测试节点创建...');

    // 测试几个关键节点类型的创建
    const testNodeTypes = [
      AI_EXTENSION_NODE_TYPES.TRANSFORMER_MODEL,
      AI_EXTENSION_NODE_TYPES.RANDOM_FOREST,
      AI_EXTENSION_NODE_TYPES.MODEL_DEPLOYMENT,
      AI_EXTENSION_NODE_TYPES.TEXT_CLASSIFICATION,
      AI_EXTENSION_NODE_TYPES.MODEL_REGISTRY
    ];

    for (const nodeType of testNodeTypes) {
      try {
        const nodeInfo = NodeRegistry.getNodeInfo(nodeType);
        if (!nodeInfo) {
          throw new Error(`无法获取节点信息: ${nodeType}`);
        }

        // 尝试创建节点实例
        const nodeInstance = NodeRegistry.createNode(nodeType);
        if (!nodeInstance) {
          throw new Error(`无法创建节点实例: ${nodeType}`);
        }

        Debug.log('AIExtensionRegistryTest', `✅ 节点创建成功: ${nodeType}`);
      } catch (error) {
        throw new Error(`节点创建失败 ${nodeType}: ${error}`);
      }
    }

    Debug.log('AIExtensionRegistryTest', '✅ 节点创建测试通过');
  }

  /**
   * 生成测试报告
   */
  public generateTestReport(): string {
    const stats = this.registry.getNodeStats();
    const registeredTypes = this.registry.getAllRegisteredNodeTypes();

    return `
# AI扩展节点注册表测试报告

## 注册状态
- 注册状态: ${this.registry.isRegistered() ? '✅ 已注册' : '❌ 未注册'}
- 注册时间: ${new Date().toISOString()}

## 节点统计
- 深度学习扩展节点: ${stats.deepLearningExtension}个
- 机器学习扩展节点: ${stats.machineLearningExtension}个
- AI工具节点: ${stats.aiTools}个
- 自然语言处理节点: ${stats.naturalLanguageProcessing}个
- 模型管理节点: ${stats.modelManagement}个
- **总计**: ${stats.total}个

## 已注册节点类型
${registeredTypes.map(type => `- ${type}`).join('\n')}

## 测试结果
✅ 所有测试通过
- 注册表初始化测试: 通过
- 节点注册测试: 通过
- 节点类型测试: 通过
- 节点统计信息测试: 通过
- 节点创建测试: 通过

## 文件位置
- 注册表文件: \`engine/src/visual-script/registry/AIExtensionNodesRegistry.ts\`
- 机器学习扩展节点: \`engine/src/visual-script/nodes/ai/MachineLearningExtensionNodes.ts\`
- 模型管理节点: \`engine/src/visual-script/nodes/ai/ModelManagementNodes.ts\`
- 测试文件: \`engine/src/visual-script/registry/test-ai-extension-registry.ts\`
`;
  }
}

/**
 * 运行测试的便捷函数
 */
export async function runAIExtensionRegistryTest(): Promise<void> {
  const test = new AIExtensionRegistryTest();
  await test.runAllTests();
  
  // 生成并输出测试报告
  const report = test.generateTestReport();
  Debug.log('AIExtensionRegistryTest', report);
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAIExtensionRegistryTest().catch(error => {
    console.error('测试执行失败:', error);
    process.exit(1);
  });
}
