/**
 * AI扩展节点使用示例
 * 演示如何使用批次1注册的AI与计算机视觉系统节点
 */

import { NodeRegistry } from '../engine/src/visual-script/registry/NodeRegistry';
import { aiExtensionNodesRegistry, AI_EXTENSION_NODE_TYPES } from '../engine/src/visual-script/registry/AIExtensionNodesRegistry';
import { Debug } from '../engine/src/utils/Debug';

/**
 * AI扩展节点使用示例类
 */
export class AIExtensionNodesUsageExample {
  
  constructor() {
    // 确保AI扩展节点已注册
    this.initializeNodes();
  }

  /**
   * 初始化节点
   */
  private initializeNodes(): void {
    Debug.log('AIExtensionExample', '初始化AI扩展节点...');
    aiExtensionNodesRegistry.registerAllNodes();
    Debug.log('AIExtensionExample', 'AI扩展节点初始化完成');
  }

  /**
   * 示例1：使用随机森林节点进行机器学习
   */
  public async exampleRandomForest(): Promise<void> {
    Debug.log('AIExtensionExample', '示例1：随机森林机器学习');

    try {
      // 创建随机森林节点
      const randomForestNode = NodeRegistry.createNode(AI_EXTENSION_NODE_TYPES.RANDOM_FOREST);
      
      if (!randomForestNode) {
        throw new Error('无法创建随机森林节点');
      }

      // 准备训练数据
      const trainingData = [
        [1, 2, 3],
        [4, 5, 6],
        [7, 8, 9],
        [10, 11, 12]
      ];
      
      const labels = ['A', 'B', 'A', 'B'];

      // 设置输入参数
      const inputs = {
        trainingData,
        labels,
        numTrees: 100,
        maxDepth: 10,
        minSamplesSplit: 2,
        maxFeatures: 'sqrt'
      };

      // 执行训练
      const result = randomForestNode.execute(inputs);
      
      Debug.log('AIExtensionExample', '随机森林训练结果:', result);
      
      if (result.success) {
        Debug.log('AIExtensionExample', `✅ 随机森林训练成功，准确率: ${(result.accuracy * 100).toFixed(2)}%`);
      } else {
        Debug.error('AIExtensionExample', '❌ 随机森林训练失败:', result.error);
      }

    } catch (error) {
      Debug.error('AIExtensionExample', '随机森林示例执行失败:', error);
    }
  }

  /**
   * 示例2：使用文本分类节点进行自然语言处理
   */
  public async exampleTextClassification(): Promise<void> {
    Debug.log('AIExtensionExample', '示例2：文本分类');

    try {
      // 创建文本分类节点
      const textClassificationNode = NodeRegistry.createNode(AI_EXTENSION_NODE_TYPES.TEXT_CLASSIFICATION);
      
      if (!textClassificationNode) {
        throw new Error('无法创建文本分类节点');
      }

      // 准备文本数据
      const texts = [
        "这是一个很好的产品",
        "服务质量很差",
        "我很满意这次购买",
        "完全不推荐"
      ];
      
      const labels = ['positive', 'negative', 'positive', 'negative'];

      // 设置输入参数
      const inputs = {
        texts,
        labels,
        model: 'bert-base',
        maxLength: 128,
        batchSize: 4
      };

      // 执行分类
      const result = textClassificationNode.execute(inputs);
      
      Debug.log('AIExtensionExample', '文本分类结果:', result);
      
      if (result.success) {
        Debug.log('AIExtensionExample', `✅ 文本分类成功，准确率: ${(result.accuracy * 100).toFixed(2)}%`);
      } else {
        Debug.error('AIExtensionExample', '❌ 文本分类失败:', result.error);
      }

    } catch (error) {
      Debug.error('AIExtensionExample', '文本分类示例执行失败:', error);
    }
  }

  /**
   * 示例3：使用模型注册表管理AI模型
   */
  public async exampleModelRegistry(): Promise<void> {
    Debug.log('AIExtensionExample', '示例3：模型注册表管理');

    try {
      // 创建模型注册表节点
      const modelRegistryNode = NodeRegistry.createNode(AI_EXTENSION_NODE_TYPES.MODEL_REGISTRY);
      
      if (!modelRegistryNode) {
        throw new Error('无法创建模型注册表节点');
      }

      // 注册新模型
      const registerInputs = {
        action: 'register',
        modelName: 'my-text-classifier',
        modelVersion: '1.0.0',
        modelPath: '/models/text-classifier-v1.0.0',
        metadata: {
          description: '文本分类模型',
          accuracy: 0.95,
          trainingDate: new Date().toISOString()
        }
      };

      const registerResult = modelRegistryNode.execute(registerInputs);
      
      if (registerResult.success) {
        Debug.log('AIExtensionExample', '✅ 模型注册成功:', registerResult.registryInfo);
      } else {
        Debug.error('AIExtensionExample', '❌ 模型注册失败:', registerResult.error);
        return;
      }

      // 列出所有模型
      const listInputs = {
        action: 'list'
      };

      const listResult = modelRegistryNode.execute(listInputs);
      
      if (listResult.success) {
        Debug.log('AIExtensionExample', '✅ 模型列表获取成功:', listResult.modelList);
      } else {
        Debug.error('AIExtensionExample', '❌ 模型列表获取失败:', listResult.error);
      }

    } catch (error) {
      Debug.error('AIExtensionExample', '模型注册表示例执行失败:', error);
    }
  }

  /**
   * 示例4：使用Transformer模型节点
   */
  public async exampleTransformerModel(): Promise<void> {
    Debug.log('AIExtensionExample', '示例4：Transformer模型');

    try {
      // 创建Transformer模型节点
      const transformerNode = NodeRegistry.createNode(AI_EXTENSION_NODE_TYPES.TRANSFORMER_MODEL);
      
      if (!transformerNode) {
        throw new Error('无法创建Transformer模型节点');
      }

      // 设置输入参数
      const inputs = {
        inputSequence: [1, 2, 3, 4, 5],
        modelConfig: {
          numLayers: 6,
          numHeads: 8,
          hiddenSize: 512,
          vocabSize: 10000
        },
        taskType: 'classification',
        maxLength: 128
      };

      // 执行推理
      const result = transformerNode.execute(inputs);
      
      Debug.log('AIExtensionExample', 'Transformer模型结果:', result);
      
      if (result.success) {
        Debug.log('AIExtensionExample', '✅ Transformer模型推理成功');
      } else {
        Debug.error('AIExtensionExample', '❌ Transformer模型推理失败:', result.error);
      }

    } catch (error) {
      Debug.error('AIExtensionExample', 'Transformer模型示例执行失败:', error);
    }
  }

  /**
   * 示例5：使用模型部署节点
   */
  public async exampleModelDeployment(): Promise<void> {
    Debug.log('AIExtensionExample', '示例5：模型部署');

    try {
      // 创建模型部署节点
      const deploymentNode = NodeRegistry.createNode(AI_EXTENSION_NODE_TYPES.MODEL_DEPLOYMENT);
      
      if (!deploymentNode) {
        throw new Error('无法创建模型部署节点');
      }

      // 设置部署参数
      const inputs = {
        modelPath: '/models/my-model',
        deploymentTarget: 'production',
        scalingConfig: {
          minInstances: 1,
          maxInstances: 10,
          targetCPU: 70
        },
        healthCheck: {
          enabled: true,
          endpoint: '/health',
          interval: 30
        }
      };

      // 执行部署
      const result = deploymentNode.execute(inputs);
      
      Debug.log('AIExtensionExample', '模型部署结果:', result);
      
      if (result.success) {
        Debug.log('AIExtensionExample', '✅ 模型部署成功');
      } else {
        Debug.error('AIExtensionExample', '❌ 模型部署失败:', result.error);
      }

    } catch (error) {
      Debug.error('AIExtensionExample', '模型部署示例执行失败:', error);
    }
  }

  /**
   * 运行所有示例
   */
  public async runAllExamples(): Promise<void> {
    Debug.log('AIExtensionExample', '开始运行AI扩展节点使用示例...');

    try {
      await this.exampleRandomForest();
      await this.exampleTextClassification();
      await this.exampleModelRegistry();
      await this.exampleTransformerModel();
      await this.exampleModelDeployment();

      Debug.log('AIExtensionExample', '✅ 所有示例运行完成！');
    } catch (error) {
      Debug.error('AIExtensionExample', '❌ 示例运行失败:', error);
    }
  }

  /**
   * 获取可用的AI扩展节点列表
   */
  public getAvailableNodes(): string[] {
    return aiExtensionNodesRegistry.getAllRegisteredNodeTypes();
  }

  /**
   * 获取节点统计信息
   */
  public getNodeStats(): any {
    return aiExtensionNodesRegistry.getNodeStats();
  }
}

// 导出便捷函数
export async function runAIExtensionExamples(): Promise<void> {
  const example = new AIExtensionNodesUsageExample();
  await example.runAllExamples();
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
  runAIExtensionExamples().catch(error => {
    console.error('示例执行失败:', error);
    process.exit(1);
  });
}
